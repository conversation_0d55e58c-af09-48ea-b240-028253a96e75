from typing import Optional

from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.budgets.commands import CreateBudgetCommand, RunBudgetCalculationCommand
from nga.apps.budgets.domain import Budget
from nga.apps.budgets.domain.events import BudgetCreatedEvent
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.domain.specifications.budgets import AbstractBudgetSpecification
from nga.apps.budgets.enums import BudgetCalculationTypeEnum
from nga.core.types import DatePeriod
from nga.internal.domain import EventPublisher
from nga.internal.uow import AbstractUnitOfWork


@Mediator.handler
class CreateBudgetCommandHandler:
    @inject
    def __init__(
        self,
        uow: AbstractUnitOfWork = Closing[Provide["uow"]],
        mediator: Mediator = Closing[Provide["mediator"]],
        event_dispatcher: EventPublisher = Provide["event_dispatcher"],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_specification: AbstractBudgetSpecification = Closing[Provide["budget_specification"]],
    ) -> None:
        """Init deps."""

        self._uow = uow
        self._mediator = mediator

        self._event_dispatcher = event_dispatcher

        self._budget_repository = budget_repository
        self._budget_specification = budget_specification

    def handle(self, cmd: CreateBudgetCommand) -> Budget:
        """Creates budget instance and optionally runs calculation."""

        with self._uow:
            budget = self._budget_repository.create(
                name=cmd.name,
                description=cmd.description,
                period=DatePeriod(cmd.start_date, cmd.end_date),
                type=cmd.type,
                home_operators=cmd.home_operators,
                user_id=cmd.user_id,
                last_historical_month=cmd.last_historical_month,
            )

        self._budget_specification.verify(budget)

        budget_created = BudgetCreatedEvent(budget=budget)

        self._event_dispatcher.publish(budget_created)

        if cmd.run_calculation:
            self._run_calculation(budget.id, cmd.user_id)

        return budget

    def _run_calculation(self, budget_id: int, user_id: Optional[int]) -> None:
        run_calculation_cmd = RunBudgetCalculationCommand(
            budget_id=budget_id,
            user_id=user_id,
            calculation_type=BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
            budget_agreement_id=None,
            traffic_lhm=None,
            distribution_lhm=None,
        )

        self._mediator.send(run_calculation_cmd)
