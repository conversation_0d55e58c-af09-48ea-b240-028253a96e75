import dataclasses
from typing import Optional

from rest_framework import serializers
from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.budgets.api.schemas import BudgetCalculationRetrieveSchema
from nga.apps.budgets.enums import BudgetCalculationStatusEnum, BudgetCalculationTypeEnum
from nga.apps.common.serializer_fields import EnumChoiceField, MonthField
from nga.apps.users.serializers import UserSerializer
from nga.core.types import Month


class BudgetCalculationRetrieveSerializer(DataclassSerializer[BudgetCalculationRetrieveSchema]):
    type = EnumChoiceField(enum_class=BudgetCalculationTypeEnum)

    status = EnumChoiceField(enum_class=BudgetCalculationStatusEnum)

    traffic_lhm = MonthField(required=False, default=None, allow_null=True)

    distribution_lhm = MonthField(required=False, default=None, allow_null=True)

    created_by = UserSerializer()

    class Meta:
        dataclass = BudgetCalculationRetrieveSchema
        fields = (
            "id",
            "budget_id",
            "budget_agreement_id",
            "type",
            "status",
            "traffic_lhm",
            "distribution_lhm",
            "created_at",
            "finished_at",
            "traffic_synchronized_at",
            "forecast_rules_applied_at",
            "created_by",
        )


@dataclasses.dataclass
class BudgetCalculationParameters:
    type: BudgetCalculationTypeEnum

    budget_agreement_id: Optional[int]

    traffic_lhm: Optional[Month]

    distribution_lhm: Optional[Month]


class BudgetRunCalculationSerializer(DataclassSerializer[BudgetCalculationParameters]):
    calculation_type = EnumChoiceField(enum_class=BudgetCalculationTypeEnum, source="type")

    budget_agreement_id = serializers.IntegerField(required=False, allow_null=True, default=None)

    traffic_lhm = MonthField(required=False, default=None, allow_null=True)

    distribution_lhm = MonthField(required=False, default=None, allow_null=True)

    class Meta:
        dataclass = BudgetCalculationParameters
        fields = (
            "calculation_type",
            "budget_agreement_id",
            "traffic_lhm",
            "distribution_lhm",
        )


class BudgetLastCalculationQuerySerializer(serializers.Serializer):
    budget_agreement_id = serializers.IntegerField(required=False, allow_null=True)

    class Meta:
        fields = ("budget_agreement_id",)
