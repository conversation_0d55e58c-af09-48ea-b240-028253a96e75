from dataclasses import dataclass
from datetime import date, datetime
from typing import Optional

from nga.apps.budgets.enums import BudgetCalculationStatusEnum, BudgetCalculationTypeEnum, BudgetTypeEnum
from nga.apps.references.domain.models import Operator
from nga.apps.users.entity import User
from nga.core.types import Month

__all__ = [
    "BudgetCalculationRetrieveSchema",
    "BudgetCreateSchema",
    "BudgetParametersSchema",
]


@dataclass
class BudgetCalculationRetrieveSchema:
    id: int
    budget_id: int

    budget_agreement_id: Optional[int]

    type: BudgetCalculationTypeEnum
    status: BudgetCalculationStatusEnum

    created_by: Optional[User]

    created_at: datetime
    finished_at: Optional[datetime]

    traffic_synchronized_at: Optional[datetime]
    forecast_rules_applied_at: Optional[datetime]

    traffic_lhm: Optional[Month]
    distribution_lhm: Optional[Month]


@dataclass
class BudgetCreateSchema:
    name: str
    description: Optional[str]

    type: BudgetTypeEnum

    home_operators: list[int]

    start_date: date
    end_date: date

    last_historical_month: Optional[Month]

    user_id: Optional[int]

    run_calculation: bool


@dataclass
class BudgetParametersSchema:
    id: int
    name: str
    description: Optional[str]

    start_date: date
    end_date: date

    last_historical_month: Optional[Month]

    home_operators: list[Operator]

    is_master: bool
    type: BudgetTypeEnum

    created_at: datetime
    updated_at: datetime
