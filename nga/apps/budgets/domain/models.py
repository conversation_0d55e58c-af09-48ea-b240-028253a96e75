from dataclasses import dataclass
from datetime import datetime
from typing import Optional

from nga.apps.budgets.domain.dto import BudgetTrafficRecordDTO
from nga.apps.budgets.enums import (
    BudgetCalculationStatusEnum,
    BudgetCalculationTypeEnum,
    BudgetSnapshotTypeEnum,
    BudgetTypeEnum,
)
from nga.core.types import DatePeriod, Month

__all__ = [
    "Budget",
    "BudgetCalculation",
    "BudgetSnapshot",
    "BudgetTrafficRecord",
]


@dataclass
class Budget:
    id: int

    name: str
    description: Optional[str]

    home_operators: list[int]

    period: DatePeriod

    is_master: bool
    type: BudgetTypeEnum

    active_snapshot_id: int
    calculation_snapshot_id: int

    last_historical_month: Month

    forecast_rules_modified_at: Optional[datetime]
    historical_traffic_modified_at: Optional[datetime]
    agreements_last_modified_at: Optional[datetime]

    created_at: datetime
    updated_at: datetime

    @property
    def historical_period(self) -> DatePeriod:
        """Returns period from budget start date up to last historical month if it's set, otherwise budget's period."""

        if self.last_historical_month is not None:
            return DatePeriod(self.period.start_date, self.last_historical_month)

        return self.period


@dataclass(kw_only=True)
class BudgetTrafficRecord(BudgetTrafficRecordDTO):
    id: int


@dataclass
class BudgetCalculation:
    id: int
    budget_id: int
    budget_snapshot_id: int

    type: BudgetCalculationTypeEnum
    status: BudgetCalculationStatusEnum

    budget_agreement_id: Optional[int]

    created_by_user_id: Optional[int]

    created_at: datetime
    finished_at: Optional[datetime]

    traffic_synchronized_at: Optional[datetime]
    forecast_rules_applied_at: Optional[datetime]
    agreements_applied_at: Optional[datetime]

    traffic_lhm: Optional[Month]
    distribution_lhm: Optional[Month]

    job_id: Optional[str]

    @property
    def is_running(self) -> bool:
        """Returns boolean that marks whether calculation is running."""

        return self.status in (
            BudgetCalculationStatusEnum.WAITING_FOR_START,
            BudgetCalculationStatusEnum.STARTED,
            BudgetCalculationStatusEnum.HISTORICAL_TRAFFIC_SYNCHRONIZATION,
            BudgetCalculationStatusEnum.FORECAST_RULES_APPLICATION,
            BudgetCalculationStatusEnum.AGREEMENTS_APPLICATION,
            BudgetCalculationStatusEnum.IS_FINISHING,
            BudgetCalculationStatusEnum.EXTERNAL_CALCULATION_RESULTS_APPLICATION,
        )

    @property
    def is_full(self) -> bool:
        return self.type in (
            BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
            BudgetCalculationTypeEnum.FULL_WITHOUT_TRAFFIC_UPDATE,
        )

    def get_budget_agreement_id(self) -> int:
        if self.budget_agreement_id is None:
            raise ValueError("budget_agreement_id is not specified for this calculation")

        return self.budget_agreement_id


@dataclass
class BudgetSnapshot:
    id: int
    budget_id: int

    name: str
    type: BudgetSnapshotTypeEnum

    created_by_user_id: Optional[int]

    created_at: datetime
    updated_at: Optional[datetime]
