from typing import Any

from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.import_export_data.api.serializers import ImportDataJobSerializer
from nga.apps.import_export_data.models import ImportDataJob
from nga.apps.import_export_data.tasks.data_sync import run_import_data_job


class ImportDataJobView(GenericAPIView):
    serializer_class = ImportDataJobSerializer

    permission_classes = [IsAuthenticated]

    def post(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        s = self.serializer_class(data=request.data)
        s.is_valid(raise_exception=True)

        file_key = s.validated_data["file_key"]

        job = get_object_or_404(ImportDataJob, file=ImportDataJob.build_file_path(file_key))

        run_import_data_job.delay(
            instance_pk=job.pk,
            instance_model=job.model,
            dry_run=False,
            run_async=True,
        )

        return Response(status=status.HTTP_202_ACCEPTED)
