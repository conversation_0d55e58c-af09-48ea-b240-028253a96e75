import traceback
from abc import ABC, abstractmethod
from functools import partial
from typing import Callable, Collection, Literal

from mediatr import Mediator

from nga.apps.agreements.commands import (
    BulkDeactivateBudgetAgreementCommand,
    BulkDeleteBudgetAgreementCommand,
    ConnectAgreementWithBudgetsCommand,
    CreateBudgetAgreementCommand,
    DeleteDiscountCommand,
)
from nga.apps.agreements.domain.discount_model_properties import DiscountModelProperties
from nga.apps.agreements.domain.discount_model_type import evaluate_discount_model_type
from nga.apps.agreements.domain.dto import BudgetAgreementCreateDTO
from nga.apps.agreements.domain.models import BudgetAgreement, Discount
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository, AbstractDiscountRepository
from nga.apps.agreements.domain.services import BudgetAgreementRenewService
from nga.apps.agreements.domain.structures import BudgetAgreementStateContainer
from nga.apps.agreements.enums import AgreementStatusEnum
from nga.apps.iotron.domain.factories.discount_dto import DiscountDTOFactory
from nga.apps.iotron.domain.models import ExternalAgreement
from nga.apps.iotron.enums import ExternalAgreementProcessingStatusEnum
from nga.apps.iotron.processing.discount_commitment_rate_filler import DiscountAboveCommitmentRateFiller
from nga.apps.iotron.processing.discount_parent_unifier import DiscountParentUnifier
from nga.apps.iotron.processing.discount_qualifying_upper_bound_filler import DiscountQualifyingUpperBoundFiller
from nga.apps.iotron.processing.discount_rel_resolver import DiscountRelationResolver
from nga.core.exceptions import BaseNGAException
from nga.utils.collections import to_id_list
from nga.utils.dt import get_current_datetime_utc


class AbstractExternalAgreementService(ABC):
    @abstractmethod
    def process(self, external_agreement: ExternalAgreement, budget_id: int) -> ExternalAgreement:
        """Process External Agreement after import."""


class ExternalAgreementService(AbstractExternalAgreementService):
    INTERSECTION_ERROR_TEMPLATE = "During processing were found {intersection_type} agreements: {ids_list}"

    def __init__(
        self,
        mediator: Mediator,
        budget_agreement_repository: AbstractBudgetAgreementRepository,
        budget_agreement_renew_service: BudgetAgreementRenewService,
        discount_repository: AbstractDiscountRepository,
    ) -> None:
        self._mediator = mediator

        self._budget_agreement_repository = budget_agreement_repository

        self._discount_repository = discount_repository

        self._budget_agreement_renew_service = budget_agreement_renew_service

    def process(self, external_agreement: ExternalAgreement, budget_id: int) -> ExternalAgreement:
        equal_by_external_id_agreement = self._budget_agreement_repository.get_by_external_id(
            external_agreement.external_id,
            budget_id,
        )
        if equal_by_external_id_agreement:
            if external_agreement.is_terminated:
                external_agreement.set_processing_status(ExternalAgreementProcessingStatusEnum.TERMINATED)

                self.delete_budget_agreements(equal_by_external_id_agreement.agreement_id)

            else:
                self.create_or_update_agreement(
                    partial(self.update_budget_agreement, external_agreement, equal_by_external_id_agreement),
                    external_agreement,
                    ExternalAgreementProcessingStatusEnum.UPDATED,
                )

            return external_agreement

        intersected_agreements = self.get_intersected_agreements(external_agreement, budget_id=budget_id)

        equal_agreements = self.get_equal_agreements(intersected_agreements.records, external_agreement)

        if equal_agreements.any_submitted:
            if len(equal_agreements.only_submitted) > 1:
                external_agreement.failed_message = "Found multiple equal submitted agreements."
                external_agreement.set_processing_status(ExternalAgreementProcessingStatusEnum.FAILED)

            submitted_agreement = list(equal_agreements.only_submitted)[0]

            submitted_agreement.set_status(AgreementStatusEnum.LIVE)

            self._budget_agreement_repository.save(submitted_agreement)

            external_agreement.set_processing_status(ExternalAgreementProcessingStatusEnum.MOVED_TO_LIVE)

        elif equal_agreements.any_approved:
            external_agreement.set_processing_status(ExternalAgreementProcessingStatusEnum.SKIPPED)

            external_agreement.failed_message = self._format_intersection_error_message_for(
                equal_agreements.only_approved,
                intersection_type="equal",
            )

        elif intersected_agreements.any_approved:
            external_agreement.set_processing_status(ExternalAgreementProcessingStatusEnum.INTERSECTED)

            external_agreement.failed_message = self._format_intersection_error_message_for(
                intersected_agreements.only_approved,
                intersection_type="intersected",
            )

        elif (
            not intersected_agreements.records
            or not equal_agreements.records
            or intersected_agreements.any_non_confirmed
            or equal_agreements.any_non_confirmed
        ):
            if external_agreement.is_terminated:
                external_agreement.set_processing_status(ExternalAgreementProcessingStatusEnum.TERMINATED)

            else:
                self.create_or_update_agreement(
                    partial(self.create_new_agreement, external_agreement, budget_id),
                    external_agreement,
                )

        else:
            external_agreement.set_processing_status(ExternalAgreementProcessingStatusEnum.FAILED)

        return external_agreement

    def get_intersected_agreements(
        self, external_agreement: ExternalAgreement, budget_id: int
    ) -> BudgetAgreementStateContainer:

        return BudgetAgreementStateContainer(
            self._budget_agreement_repository.get_intersected_many(
                budget_id=budget_id,
                home_operators=external_agreement.home_operators,
                partner_operators=external_agreement.partner_operators,
                period=external_agreement.period,
            )
        )

    @staticmethod
    def get_equal_agreements(
        agreements_list: Collection[BudgetAgreement], external_agreement: ExternalAgreement
    ) -> BudgetAgreementStateContainer:
        return BudgetAgreementStateContainer(
            [
                a
                for a in agreements_list
                if sorted(a.home_operators) == sorted(external_agreement.home_operators)
                and sorted(a.partner_operators) == sorted(external_agreement.partner_operators)
                and a.period == external_agreement.period
            ]
        )

    def create_new_agreement(self, external_agreement: ExternalAgreement, budget_id: int) -> None:
        budget_agreement = self._create_budget_agreement(external_agreement, budget_id)

        self._create_discounts(external_agreement, agreement_id=budget_agreement.agreement_id)

        self._set_agreement_status_and_activation(budget_agreement, external_agreement.do_not_calculate)

        self._budget_agreement_repository.save(budget_agreement)

        self._mediator.send(ConnectAgreementWithBudgetsCommand(budget_agreement.id))

    def _create_budget_agreement(self, external_agreement: ExternalAgreement, budget_id: int) -> BudgetAgreement:

        budget_agreement_dto = BudgetAgreementCreateDTO(
            name=external_agreement.name,
            external_id=external_agreement.external_id,
            home_operators=external_agreement.home_operators,
            partner_operators=external_agreement.partner_operators,
            period=external_agreement.period,
            negotiator_id=external_agreement.negotiator,
            include_satellite=external_agreement.include_satellite,
            include_premium=external_agreement.include_premium,
            include_premium_in_commitment=external_agreement.include_premium_in_commitment,
            # is_rolling=external_agreement.is_rolling,
            is_rolling=False,  # FIXME: Temporary ignore IOTRON settings
        )

        create_budget_agreement_cmd = CreateBudgetAgreementCommand(
            budget_id=budget_id,
            budget_agreement_dto=budget_agreement_dto,
        )

        budget_agreement = self._mediator.send(create_budget_agreement_cmd)

        return budget_agreement

    def update_budget_agreement(self, external_agreement: ExternalAgreement, budget_agreement: BudgetAgreement) -> None:

        updated_budget_agreement = BudgetAgreement(
            id=budget_agreement.id,
            external_id=external_agreement.external_id,
            parent_id=budget_agreement.parent_id,
            name=external_agreement.name,
            status=budget_agreement.status,
            home_operators=external_agreement.home_operators,
            partner_operators=external_agreement.partner_operators,
            period=external_agreement.period,
            negotiator_id=external_agreement.negotiator,
            updated_at=get_current_datetime_utc(),
            budget_id=budget_agreement.budget_id,
            agreement_id=budget_agreement.agreement_id,
            is_active=budget_agreement.is_active,
            applied_at=budget_agreement.applied_at,
            calculation_status=budget_agreement.calculation_status,
            include_satellite=external_agreement.include_satellite,
            include_premium=external_agreement.include_premium,
            include_premium_in_commitment=external_agreement.include_premium_in_commitment,
            # is_rolling=external_agreement.is_rolling,
            is_rolling=budget_agreement.is_rolling,  # FIXME: Temporary update is disabled
        )

        # Remove old discounts
        discounts = self._discount_repository.get_many(updated_budget_agreement.agreement_id)
        for discount in discounts:
            self._mediator.send(DeleteDiscountCommand(discount=discount, budget_agreement=updated_budget_agreement))

        # Create new discounts
        self._create_discounts(external_agreement, agreement_id=updated_budget_agreement.agreement_id)

        updated_budget_agreement.status = AgreementStatusEnum.LIVE

        self._budget_agreement_repository.save(updated_budget_agreement)

        self.update_linked_agreements(updated_budget_agreement)

        self._deactivate_related_budget_agreements(budget_agreement.agreement_id)

    def update_linked_agreements(self, budget_agreement: BudgetAgreement) -> None:
        nested_agreement = self._budget_agreement_repository.get_by_parent_id(
            budget_agreement.agreement_id,
            budget_id=budget_agreement.budget_id,
        )

        if nested_agreement is not None:
            nested_agreement.name = budget_agreement.renew_name()
            nested_agreement.period = budget_agreement.renew_period()

            nested_agreement.home_operators = budget_agreement.home_operators
            nested_agreement.partner_operators = budget_agreement.partner_operators
            nested_agreement.negotiator_id = budget_agreement.negotiator_id

            nested_agreement.include_satellite = budget_agreement.include_satellite
            nested_agreement.include_premium = budget_agreement.include_premium
            nested_agreement.include_premium_in_commitment = budget_agreement.include_premium_in_commitment
            nested_agreement.is_rolling = budget_agreement.is_rolling

            nested_agreement.updated_at = get_current_datetime_utc()

            self._budget_agreement_repository.save(nested_agreement)

            self._budget_agreement_renew_service.renew_discounts(
                budget_agreement, nested_agreement, self._discount_repository
            )

            self.update_linked_agreements(nested_agreement)

    def _create_discounts(self, external_agreement: ExternalAgreement, agreement_id: int) -> None:

        for external_discount in external_agreement.discounts:

            discount_dto = DiscountDTOFactory.create_from_external_discount(
                external_discount,
                external_agreement.include_access_fee_in_sop_financial_inbound,
                external_agreement.include_access_fee_in_sop_financial_outbound,
            )

            self._discount_repository.create(agreement_id, discount_dto)

        discount_unifier = DiscountParentUnifier(self._discount_repository)

        discount_unifier.unify_parent_discounts(agreement_id)

        relation_resolver = DiscountRelationResolver(self._discount_repository)

        relation_resolver.resolve_sub_discount_rel(agreement_id)

        discounts = self._discount_repository.get_many(agreement_id)

        self._resolve_discount_model_type(discounts)

        self._fill_above_commitment_rate(discounts)

        self._fill_qualifying_upper_bound(discounts)

    def _resolve_discount_model_type(self, discounts: tuple[Discount, ...]) -> None:
        for discount in discounts:
            self._try_evaluate_discount_model_type(discount)

            if discount.has_sub_discounts is False:
                continue

            for sub_discount in discount.sub_discounts:
                self._try_evaluate_discount_model_type(sub_discount)

    def _fill_above_commitment_rate(self, discounts: tuple[Discount, ...]) -> None:
        above_commitment_rate_filler = DiscountAboveCommitmentRateFiller(self._discount_repository)

        for discount in discounts:

            if not DiscountModelProperties(discount).is_sop_financial:
                continue

            above_commitment_rate_filler.fill_above_commitment_rate(discount)

    def _fill_qualifying_upper_bound(self, discounts: tuple[Discount, ...]) -> None:
        qualifying_upper_bound_filler = DiscountQualifyingUpperBoundFiller(self._discount_repository)

        qualifying_upper_bound_filler.fill_qualifying_upper_bound(list(discounts))

    def _try_evaluate_discount_model_type(self, discount: Discount) -> None:
        model_type = evaluate_discount_model_type(discount)

        if model_type is not None:
            discount.model_type = model_type

            self._discount_repository.save(discount)

    def _deactivate_related_budget_agreements(self, agreement_id: int) -> None:
        budget_agreements = self._budget_agreement_repository.get_many(agreement_id=agreement_id)

        for budget_agreement in budget_agreements:
            deactivate_cmd = BulkDeactivateBudgetAgreementCommand(
                budget_id=budget_agreement.budget_id,
                budget_agreement_ids=[budget_agreement.id],
            )

            self._mediator.send(deactivate_cmd)

    def delete_budget_agreements(self, agreement_id: int) -> None:
        budget_agreements = self._budget_agreement_repository.get_many(agreement_id=agreement_id)

        for ba in budget_agreements:
            cmd = BulkDeleteBudgetAgreementCommand(budget_id=ba.budget_id, budget_agreement_ids=[ba.id])

            try:
                self._mediator.send(cmd)

            except Exception:
                traceback.print_exc()

    @staticmethod
    def _set_agreement_status_and_activation(budget_agreement: BudgetAgreement, do_not_calculate: bool) -> None:
        budget_agreement.status = AgreementStatusEnum.LIVE

        if do_not_calculate:
            budget_agreement.deactivate()
        else:
            budget_agreement.activate()

    @staticmethod
    def create_or_update_agreement(
        function: Callable[[], None],
        external_agreement: ExternalAgreement,
        success_status: ExternalAgreementProcessingStatusEnum = ExternalAgreementProcessingStatusEnum.NEW_CREATED,
    ) -> None:
        try:
            function()
            external_agreement.set_processing_status(success_status)

        except BaseNGAException as e:
            external_agreement.set_processing_status(ExternalAgreementProcessingStatusEnum.FAILED)
            external_agreement.failed_message = str(e)

    def _format_intersection_error_message_for(
        self,
        budget_agreements: Collection[BudgetAgreement],
        *,
        intersection_type: Literal["equal", "intersected"],
    ) -> str:
        return self.INTERSECTION_ERROR_TEMPLATE.format(
            intersection_type=intersection_type,
            ids_list=", ".join(map(str, to_id_list(budget_agreements))),
        )
