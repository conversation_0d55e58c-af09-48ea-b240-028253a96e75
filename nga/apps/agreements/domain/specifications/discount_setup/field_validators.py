from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.models import Discount, DiscountParameter
from nga.apps.agreements.enums import (
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountSettlementMethodEnum,
)
from nga.core.enums import ServiceTypeEnum

_MAX_SERVICES = 2

_MULTIPLE_SERVICE_TYPE_COMBINATIONS = (
    sorted(ServiceTypeEnum.voice_services()),
    sorted(ServiceTypeEnum.sms_services()),
)


def service_types_with_combo(discount: Discount) -> None:
    """Any single service type is allowed. Or combination of VOICE or SMS services (MO with MT)."""

    total_services = len(discount.service_types)

    if total_services > _MAX_SERVICES:
        raise DiscountValidationError("Discount has invalid number of service types")

    elif total_services == _MAX_SERVICES:
        if sorted(discount.service_types) not in _MULTIPLE_SERVICE_TYPE_COMBINATIONS:
            raise DiscountValidationError("Discount has invalid combination of service types")


def only_access_fee(discount: Discount) -> None:
    """Verifies that discount has only ACCESS_FEE service_type"""

    if discount.service_types != (ServiceTypeEnum.ACCESS_FEE,):
        raise DiscountValidationError(f"Discount requires only {ServiceTypeEnum.ACCESS_FEE.name} service type")


def credit_note_eoa_settlement_method(discount: Discount) -> None:
    """Only DiscountSettlementMethodEnum.CREDIT_NOTE_EOA is allowed."""

    if discount.settlement_method != DiscountSettlementMethodEnum.CREDIT_NOTE_EOA:
        raise DiscountValidationError("Discount settlement method must be CREDIT_NOTE_EOA")


def param_basis_is_value(discount_parameter: DiscountParameter) -> None:
    """Only DiscountBasisEnum.VALUE is allowed."""

    if discount_parameter.basis is None or discount_parameter.basis != DiscountBasisEnum.VALUE:
        raise DiscountValidationError("Discount basis is empty or does not equal to VALUE")


def param_basis_value_is_required(discount_parameter: DiscountParameter) -> None:
    """Discount basis value must be set."""

    if discount_parameter.basis_value is None:
        raise DiscountValidationError("Discount basis value must be set")


def no_balancing(discount_parameter: DiscountParameter) -> None:
    """Only DiscountBalancingEnum.NO_BALANCING is allowed."""

    if discount_parameter.balancing and discount_parameter.balancing != DiscountBalancingEnum.NO_BALANCING:
        raise DiscountValidationError("Discount balancing must be NO_BALANCING")


def unbalanced_only(discount_parameter: DiscountParameter) -> None:
    """Only DiscountBalancingEnum.BALANCED is allowed."""

    if discount_parameter.balancing is None or discount_parameter.balancing != DiscountBalancingEnum.UNBALANCED:
        raise DiscountValidationError("Discount balancing must be UNBALANCED")


def bound_type_is_volume(discount_parameter: DiscountParameter) -> None:
    """Only DiscountBoundTypeEnum.VOLUME is allowed."""

    if discount_parameter.bound_type is None or discount_parameter.bound_type != DiscountBoundTypeEnum.VOLUME:
        raise DiscountValidationError("Discount bound type must be VOLUME")


def bound_type_is_unique_per_month_per_imsi(discount_parameter: DiscountParameter) -> None:
    """Only DiscountBoundTypeEnum.UNIQUE_IMSI_COUNT_PER_MONTH is allowed."""

    if discount_parameter.bound_type != DiscountBoundTypeEnum.UNIQUE_IMSI_COUNT_PER_MONTH:
        raise DiscountValidationError(
            f"Discount bound type must be {DiscountBoundTypeEnum.UNIQUE_IMSI_COUNT_PER_MONTH.name}"
        )


def bound_type_is_financial(discount_parameter: DiscountParameter) -> None:
    """Only DiscountBoundTypeEnum.FINANCIAL is allowed."""

    if discount_parameter.bound_type != DiscountBoundTypeEnum.FINANCIAL_COMMITMENT:
        raise DiscountValidationError(f"Discount bound type must be {DiscountBoundTypeEnum.FINANCIAL_COMMITMENT.name}")


def lower_bound_filled(discount_parameter: DiscountParameter) -> None:
    if discount_parameter.lower_bound is None:
        raise DiscountValidationError("Discount lower bound must be set")


def access_fee_rate_filled(discount_parameter: DiscountParameter) -> None:
    if discount_parameter.access_fee_rate is None:
        raise DiscountValidationError("Discount requires access fee rate to be filled")


def verify_single_calculation_type(discount: Discount, expected_calculation_type: DiscountCalculationTypeEnum) -> None:
    """Verifies that discount has only one parameter with specified calculation type."""

    single_discount_parameter(discount)

    if discount.parameters[0].calculation_type != expected_calculation_type:
        msg = f"Discount parameter requires {expected_calculation_type.name} calculation type"
        raise DiscountValidationError(msg)


def verify_number_of_parameters(discount: Discount, expected_total_parameters: int) -> None:
    if discount.total_parameters != expected_total_parameters:
        raise DiscountValidationError(f"Total number of discount parameters must be {expected_total_parameters}")


def single_discount_parameter(discount: Discount) -> None:
    """Ensures that discount has only one parameter."""

    verify_number_of_parameters(discount, expected_total_parameters=1)
