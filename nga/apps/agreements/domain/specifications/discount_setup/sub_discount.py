from typing import cast

from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.repositories import AbstractDiscountRepository
from nga.apps.agreements.domain.specifications.abstract import (
    AbstractDiscountSpecification,
)
from nga.apps.agreements.domain.specifications.discount_setup import (
    SoPFinancialDiscountSetupSpecification,
)
from nga.apps.agreements.domain.specifications.utils import discount_fulfills_spec, get_spec_by_model_type
from nga.apps.agreements.enums import DiscountModelTypeEnum


# This spec is created for verifying sub-discount. It does not check whether parent of sub-discount has a valid setup
# including its sub-discounts
class SubDiscountSetupSpecification(AbstractDiscountSpecification):

    ALLOWED_SUB_DISCOUNT_MODEL_TYPES = [
        DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE,
        DiscountModelTypeEnum.STEPPED_TIERED,
        DiscountModelTypeEnum.BACK_TO_FIRST,
        DiscountModelTypeEnum.BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE,
        DiscountModelTypeEnum.BALANCED_UNBALANCED_STEPPED_TIERED,
        DiscountModelTypeEnum.PER_MONTH_PER_IMSI,
        DiscountModelTypeEnum.PER_MONTH_PER_IMSI_ABOVE_THRESHOLD,
        DiscountModelTypeEnum.PER_MONTH_PER_IMSI_STEPPED_TIERED,
        DiscountModelTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING,
        DiscountModelTypeEnum.PER_MONTH_PER_IMSI_BACK_TO_FIRST,
    ]

    def __init__(self, discount_repository: AbstractDiscountRepository) -> None:
        self._discount_repository = discount_repository

    def verify(self, discount: Discount) -> None:
        """Verifies sub-discount setup."""

        if discount.is_parent:
            raise DiscountValidationError("Discount does not have a parent")

        parent_discount = self._discount_repository.get_by_id(cast(int, discount.parent_id))

        discount_is_sop_financial = discount_fulfills_spec(parent_discount, SoPFinancialDiscountSetupSpecification())

        if not discount_is_sop_financial:
            raise DiscountValidationError("Parent discount must be SoP Financial")

        self.verify_sub_discount(discount)

    def verify_sub_discount(self, sub_discount: Discount) -> None:

        if sub_discount.model_type not in self.get_allowed_sub_discount_model_types():
            raise DiscountValidationError("Model type is not allowed to be in sub discount")

        sub_discount_spec = get_spec_by_model_type(sub_discount.model_type)

        sub_discount_spec.verify(sub_discount)

    @classmethod
    def get_allowed_sub_discount_model_types(cls) -> tuple[DiscountModelTypeEnum, ...]:
        return (
            DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE,
            DiscountModelTypeEnum.STEPPED_TIERED,
            DiscountModelTypeEnum.BACK_TO_FIRST,
            DiscountModelTypeEnum.BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE,
            DiscountModelTypeEnum.BALANCED_UNBALANCED_STEPPED_TIERED,
            DiscountModelTypeEnum.PER_MONTH_PER_IMSI,
            DiscountModelTypeEnum.PER_MONTH_PER_IMSI_ABOVE_THRESHOLD,
            DiscountModelTypeEnum.PER_MONTH_PER_IMSI_STEPPED_TIERED,
            DiscountModelTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING,
            DiscountModelTypeEnum.PER_MONTH_PER_IMSI_BACK_TO_FIRST,
        )
