from abc import ABC, abstractmethod

from dateutil.relativedelta import relativedelta

from nga.apps.agreements.domain.dto import BudgetAgreementCreateDTO
from nga.apps.agreements.domain.exceptions import (
    RenewedAgreementPeriodOverrideBudgetPeriodError,
)
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository, AbstractDiscountRepository
from nga.apps.agreements.domain.utils import copy_discounts
from nga.apps.budgets.domain import Budget
from nga.core.types import DatePeriod


class AbstractBudgetAgreementRenewService(ABC):

    @abstractmethod
    def renew(self, budget: Budget, budget_agreement: BudgetAgreement) -> BudgetAgreement:
        """Method to renew a budget agreement and discounts for it."""


class BudgetAgreementRenewService(AbstractBudgetAgreementRenewService):

    def __init__(
        self,
        budget_repository: AbstractBudgetAgreementRepository,
        budget_agreement_repository: AbstractBudgetAgreementRepository,
        discount_repository: AbstractDiscountRepository,
    ):
        self._budget_repository = budget_repository
        self._budget_agreement_repository = budget_agreement_repository
        self._discount_repository = discount_repository

    def renew(self, budget: Budget, budget_agreement: BudgetAgreement) -> BudgetAgreement:
        agreement_create_dto = BudgetAgreementCreateDTO(
            name=budget_agreement.renew_name(),
            home_operators=budget_agreement.home_operators,
            partner_operators=budget_agreement.partner_operators,
            period=budget_agreement.renew_period(),
            negotiator_id=None,
            include_satellite=budget_agreement.include_satellite,
            include_premium=budget_agreement.include_premium,
            include_premium_in_commitment=budget_agreement.include_premium_in_commitment,
            is_rolling=budget_agreement.is_rolling,
        )

        if agreement_create_dto.period not in budget.period:
            raise RenewedAgreementPeriodOverrideBudgetPeriodError()

        renewed_budget_agreement = self._budget_agreement_repository.create(
            agreement_dto=agreement_create_dto,
            budget_id=budget_agreement.budget_id,
        )

        self.renew_discounts(budget_agreement, renewed_budget_agreement, self._discount_repository)

        return renewed_budget_agreement

    @classmethod
    def renew_discounts(
        cls,
        budget_agreement: BudgetAgreement,
        renewed_budget_agreement: BudgetAgreement,
        discount_repository: AbstractDiscountRepository,
    ) -> None:
        discounts = discount_repository.get_many(budget_agreement.agreement_id)

        for discount in discounts:
            discount.period = cls.renew_discount_period(discount.period, renewed_budget_agreement.period)

            for sub_discount in discount.sub_discounts:
                sub_discount.period = cls.renew_discount_period(sub_discount.period, renewed_budget_agreement.period)

        copy_discounts(discounts, renewed_budget_agreement, discount_repository)

    @staticmethod
    def renew_discount_period(discount_period: DatePeriod, agreement_period: DatePeriod) -> DatePeriod:
        delta = relativedelta(months=agreement_period.total_months)

        renewed_start_date = discount_period.start_date + delta
        renewed_end_date = discount_period.end_date + delta

        renewed_period = DatePeriod(renewed_start_date, renewed_end_date)

        return renewed_period
