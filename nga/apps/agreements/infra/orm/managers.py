from typing import Literal

from django.contrib.postgres.aggregates import Array<PERSON>gg
from django.db.models import <PERSON><PERSON>an<PERSON>ield, Case, F, Q, QuerySet, Value, When
from django.db.models.sql.constants import LOUTER
from django_cte import CTEQuerySet, With

from nga.apps.agreements.enums import AgreementStatusEnum
from nga.apps.agreements.infra.orm import models
from nga.apps.common.queryset_utils import period_dates_intersection_query


class BudgetAgreementQuerySet(CTEQuerySet["models.BudgetAgreement"]):
    def with_has_intersection(self) -> QuerySet["models.BudgetAgreement"]:
        """
        Returns queryset with `has_intersection:bool` column. <PERSON><PERSON> says whether an agreement has intersections with
        other submitted or active agreements within requested budget.
        """

        base_agreements_cte_query = self.annotate(
            home_operators=ArrayAgg("agreement__home_operators__pk", distinct=True, default=[]),
            partner_operators=ArrayAgg("agreement__partner_operators__pk", distinct=True, default=[]),
            start_date=F("agreement__start_date"),
            end_date=F("agreement__end_date"),
        ).values(
            "id",
            "start_date",
            "end_date",
            "home_operators",
            "partner_operators",
        )

        # collection of submitted or active agreements that is used as a reference to check with
        reference_agreements_cte = With(
            base_agreements_cte_query.filter(
                Q(is_active=True) | Q(agreement__status__in=AgreementStatusEnum.get_confirmed_statuses())
            ),
            name="reference_agreements_cte",
        )

        # budget agreements that are going to be checked
        checked_agreements_cte = With(base_agreements_cte_query, name="checked_agreements_cte")

        # join budget agreements with agreements it intersects with
        agreement_intersections_cte = With(
            reference_agreements_cte.join(
                checked_agreements_cte.queryset(),
                period_dates_intersection_query(
                    reference_agreements_cte.col.start_date,
                    reference_agreements_cte.col.end_date,
                ),
                ~Q(id=reference_agreements_cte.col.id),
                home_operators__overlap=reference_agreements_cte.col.home_operators,
                partner_operators__overlap=reference_agreements_cte.col.partner_operators,
                _join_type=LOUTER,
            )
            .annotate(
                intersected_with=ArrayAgg(reference_agreements_cte.col.id, distinct=True, default=[]),
            )
            .values(
                "id",
                "intersected_with",
            ),
            name="agreement_intersections",
        )

        agreements_qs = (
            agreement_intersections_cte.join(
                self.model,
                id=agreement_intersections_cte.col.id,
                _join_type=LOUTER,
            )
            .with_cte(reference_agreements_cte)
            .with_cte(checked_agreements_cte)
            .with_cte(agreement_intersections_cte)
            .annotate(intersected_with=agreement_intersections_cte.col.intersected_with)
            .annotate(
                has_intersection=Case(
                    When(intersected_with__0__isnull=True, then=Value(False)),
                    When(intersected_with__len=0, then=Value(False)),
                    default=Value(True),
                    output_field=BooleanField(),
                )
            )
        )

        return agreements_qs

    def filter_linked_agreements(
        self,
        budget_agreement_id: int,
        budget_id: int,
        *,
        chronology: Literal["parents", "subsidiaries"],
    ) -> QuerySet["models.BudgetAgreement"]:

        def make_recursive_cte(base_agreements_cte: With) -> QuerySet:
            if chronology == "parents":
                join_query = dict(agreement__parent_id=base_agreements_cte.col.agreement_id)
            else:
                join_query = dict(agreement__id=base_agreements_cte.col.parent_id)

            return (
                self.model.objects.filter(id=budget_agreement_id)
                .values(
                    "id",
                    "agreement_id",
                    parent_id=F("agreement__parent_id"),
                )
                .union(
                    base_agreements_cte.join(
                        self.model,
                        **join_query,
                    )
                    .filter(budget_id=budget_id)
                    .values(
                        "id",
                        "agreement_id",
                        parent_id=F("agreement__parent_id"),
                    ),
                    all=True,
                )
            )

        cte = With.recursive(make_recursive_cte)

        budget_agreement_ids = cte.queryset().with_cte(cte).values_list("id", flat=True).exclude(id=budget_agreement_id)

        return self.filter(id__in=budget_agreement_ids)
