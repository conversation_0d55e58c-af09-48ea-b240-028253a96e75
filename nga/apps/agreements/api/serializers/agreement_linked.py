from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.enums import AgreementStatusEnum
from nga.apps.common.serializer_fields import EnumChoiceField, YearMonthField


class LinkedAgreementResponseSerializer(DataclassSerializer[BudgetAgreement]):
    status = EnumChoiceField(enum_class=AgreementStatusEnum)

    start_date = YearMonthField(source="period.start_date")
    end_date = YearMonthField(source="period.end_date")

    class Meta:
        dataclass = BudgetAgreement
        fields = (
            "id",
            "name",
            "status",
            "start_date",
            "end_date",
        )
