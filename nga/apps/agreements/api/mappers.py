from abc import ABC, abstractmethod
from typing import Iterable, Optional

from nga.apps.agreements.api.schemas import (
    AgreementCreateSchema,
    BudgetAgreementSchema,
    CommitmentDistributionParameterSchema,
    DiscountQualifyingRuleSchema,
    DiscountSchema,
)
from nga.apps.agreements.domain.dto import BudgetAgreementCreateDTO
from nga.apps.agreements.domain.models import BudgetAgreement, Discount
from nga.apps.agreements.domain.repositories import (
    AbstractAgreementNegotiatorRepository,
    AbstractBudgetAgreementRepository,
    AbstractDiscountRepository,
)
from nga.apps.agreements.enums import AgreementIntersectionTypeEnum, AgreementStatusEnum
from nga.apps.references.api.schemas import TrafficSegmentSchema
from nga.apps.references.domain.models import Country, Operator, TrafficSegment
from nga.apps.references.providers import (
    AbstractCountryProvider,
    AbstractOperatorProvider,
    AbstractTrafficSegmentProvider,
)
from nga.core.types import DatePeriod


def map_agreement_create_schema_to_dto(schema: AgreementCreateSchema) -> BudgetAgreementCreateDTO:
    return BudgetAgreementCreateDTO(
        name=schema.name,
        home_operators=schema.home_operators,
        partner_operators=schema.partner_operators,
        period=DatePeriod(schema.start_date, schema.end_date),
        negotiator_id=schema.negotiator.id if schema.negotiator else None,
        include_satellite=schema.include_satellite,
        include_premium=schema.include_premium,
        include_premium_in_commitment=schema.include_premium_in_commitment,
        is_rolling=schema.is_rolling,
    )


class AbstractBudgetAgreementListSchemaMapper(ABC):
    @abstractmethod
    def map(
        self,
        agreements: Iterable[BudgetAgreement],
        intersection_map: Optional[dict[int, list[AgreementIntersectionTypeEnum]]] = None,
    ) -> list[BudgetAgreementSchema]:
        """Maps list of agreements to list of schema instances."""


class BudgetAgreementListSchemaMapper(AbstractBudgetAgreementListSchemaMapper):
    def __init__(
        self,
        operator_provider: AbstractOperatorProvider,
        country_provider: AbstractCountryProvider,
        agreement_negotiator_repository: AbstractAgreementNegotiatorRepository,
        budget_agreement_repository: AbstractBudgetAgreementRepository,
    ) -> None:
        self._operator_provider = operator_provider
        self._country_provider = country_provider
        self._agreement_negotiator_repository = agreement_negotiator_repository
        self._budget_agreement_repository = budget_agreement_repository

    def map(
        self,
        agreements: Iterable[BudgetAgreement],
        intersection_map: Optional[dict[int, list[AgreementIntersectionTypeEnum]]] = None,
    ) -> list[BudgetAgreementSchema]:
        """Maps list of agreements to list of schema instances."""

        countries = self._country_provider.get_many()
        operators = self._operator_provider.get_many()
        agreement_negotiators = self._agreement_negotiator_repository.get_many()

        operators_map = {o.id: o for o in operators}
        countries_map = {c.id: c for c in countries}
        agreement_negotiators_map = {a.id: a for a in agreement_negotiators}

        if intersection_map is None:
            intersection_map = {a.id: self._evaluate_intersection_types(a) for a in agreements}

        list_items = []

        for a in agreements:
            partner_operators = [operators_map[o_id] for o_id in a.partner_operators]
            partner_countries_ids = set(o.country_id for o in partner_operators)

            next_statuses = (
                [] if a.status in AgreementStatusEnum.get_unchangeable_statuses() else list(a.get_next_statuses())
            )

            item = BudgetAgreementSchema(
                id=a.id,
                budget_id=a.budget_id,
                agreement_id=a.agreement_id,
                name=a.name,
                status=a.status,
                next_statuses=next_statuses,
                calculation_status=a.calculation_status,
                start_date=a.period.start_date,
                end_date=a.period.end_date,
                is_active=a.is_active,
                negotiator=agreement_negotiators_map[a.negotiator_id] if a.negotiator_id else None,
                home_operators=[operators_map[o_id] for o_id in a.home_operators],
                partner_operators=[operators_map[o_id] for o_id in a.partner_operators],
                partner_countries=[countries_map[c_id] for c_id in partner_countries_ids],
                intersection_types=intersection_map[a.id],
                include_satellite=a.include_satellite,
                include_premium=a.include_premium,
                include_premium_in_commitment=a.include_premium_in_commitment,
                is_rolling=a.is_rolling,
                updated_at=a.updated_at,
                applied_at=a.applied_at,
            )
            list_items.append(item)

        return list_items

    def _evaluate_intersection_types(
        self,
        budget_agreement: BudgetAgreement,
    ) -> list[AgreementIntersectionTypeEnum]:
        intersection_types = []

        active_intersection = self._budget_agreement_repository.has_intersection(budget_agreement, with_active=True)
        confirmed_intersection = self._budget_agreement_repository.has_intersection(
            budget_agreement,
            with_statuses=AgreementStatusEnum.get_confirmed_statuses(),
        )

        if confirmed_intersection:
            intersection_types.append(AgreementIntersectionTypeEnum.CONFIRMED)

        if active_intersection:
            intersection_types.append(AgreementIntersectionTypeEnum.ACTIVE)

        return intersection_types


class AbstractDiscountSchemaMapper(ABC):
    @abstractmethod
    def map_many(self, discounts: Iterable[Discount]) -> tuple[DiscountSchema, ...]:
        """Maps collection of discounts."""

    @abstractmethod
    def map_one(self, discount: Discount) -> DiscountSchema:
        """Maps discount to schema."""


class DiscountSchemaMapper(AbstractDiscountSchemaMapper):
    def __init__(
        self,
        discount_repository: AbstractDiscountRepository,
        operator_provider: AbstractOperatorProvider,
        country_provider: AbstractCountryProvider,
        traffic_segment_provider: AbstractTrafficSegmentProvider,
    ) -> None:
        self._discount_repository = discount_repository
        self._operator_provider = operator_provider
        self._country_provider = country_provider
        self._traffic_segment_provider = traffic_segment_provider

        self._operators_map: dict[int, Operator] = {}
        self._countries_map: dict[int, Country] = {}
        self._traffic_segments_map: dict[int, TrafficSegment] = {}

    @property
    def operators_map(self) -> dict[int, Operator]:
        if not self._operators_map:
            self._operators_map = {o.id: o for o in self._operator_provider.get_many()}

        return self._operators_map

    @property
    def countries_map(self) -> dict[int, Country]:
        if not self._countries_map:
            self._countries_map = {c.id: c for c in self._country_provider.get_many()}

        return self._countries_map

    @property
    def traffic_segments_map(self) -> dict[int, TrafficSegment]:
        if not self._traffic_segments_map:
            self._traffic_segments_map = {t.id: t for t in self._traffic_segment_provider.get_many()}

        return self._traffic_segments_map

    def map_many(self, discounts: Iterable[Discount]) -> tuple[DiscountSchema, ...]:
        return tuple(self.map_one(d) for d in discounts)

    def map_one(self, discount: Discount) -> DiscountSchema:

        if discount.qualifying_rule is not None:
            qualifying_rule = DiscountQualifyingRuleSchema(
                direction=discount.qualifying_rule.direction,
                service_types=discount.qualifying_rule.service_types,
                basis=discount.qualifying_rule.basis,
                lower_bound=discount.qualifying_rule.lower_bound,
                upper_bound=discount.qualifying_rule.upper_bound,
            )
        else:
            qualifying_rule = None

        if discount.commitment_distribution_parameters is not None:
            commitment_distribution_parameters = tuple(
                CommitmentDistributionParameterSchema(
                    home_operators=tuple(self.operators_map[o_id] for o_id in param.home_operators),
                    partner_operators=tuple(self.operators_map[o_id] for o_id in param.partner_operators),
                    charge=param.charge,
                )
                for param in discount.commitment_distribution_parameters
            )
        else:
            commitment_distribution_parameters = None

        return DiscountSchema(
            id=discount.id,
            agreement_id=discount.agreement_id,
            home_operators=tuple(self.operators_map[o_id] for o_id in discount.home_operators),
            partner_operators=tuple(self.operators_map[o_id] for o_id in discount.partner_operators),
            direction=discount.direction,
            service_types=discount.service_types,
            period=discount.period,
            model_type=discount.model_type,
            currency_code=discount.currency_code,
            tax_type=discount.tax_type,
            volume_type=discount.volume_type,
            settlement_method=discount.settlement_method,
            call_destinations=discount.call_destinations,
            called_countries=(
                tuple(self.countries_map[c_id] for c_id in discount.called_countries)
                if discount.called_countries
                else None
            ),
            traffic_segments=(
                tuple(self._map_traffic_segment(t_id) for t_id in discount.traffic_segments)
                if discount.traffic_segments
                else None
            ),
            imsi_count_type=discount.imsi_count_type,
            qualifying_rule=qualifying_rule,
            parent_id=discount.parent_id,
            above_commitment_rate=discount.above_commitment_rate,
            inbound_market_share=discount.inbound_market_share,
            commitment_distribution_parameters=commitment_distribution_parameters,
            parameters=discount.parameters,
            financial_threshold=discount.financial_threshold,
            above_financial_threshold_rate=discount.above_financial_threshold_rate,
            sub_discounts=tuple(self.map_one(sb) for sb in discount.sub_discounts),
        )

    def _map_traffic_segment(self, traffic_segment_id: int) -> TrafficSegmentSchema:
        traffic_segment = self.traffic_segments_map[traffic_segment_id]

        schema = TrafficSegmentSchema(
            id=traffic_segment.id,
            home_operator=self.operators_map[traffic_segment.home_operator_id],
            name=traffic_segment.name,
            description=traffic_segment.description,
        )

        return schema
