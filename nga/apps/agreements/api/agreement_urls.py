from django.urls import path

from nga.apps.agreements.api.views import (
    AgreementActivateAPIView,
    AgreementActivateBulkAPIView,
    AgreementAPIView,
    AgreementBulkCloneAPIView,
    AgreementBulkCopyAPIView,
    AgreementBulkStatusChangeAPIView,
    AgreementCloneAPIView,
    AgreementCollectionAPIView,
    AgreementCopyAPIView,
    AgreementDeactivateAPIView,
    AgreementDeactivateBulkAPIView,
    AgreementFilterParametersAPIView,
    AgreementIntersectedAPIView,
    AgreementLinkedAPIView,
    AgreementListXlsxView,
    AgreementRenewAPIView,
    SubmitAgreementsToIOTRONAPIView,
)

urlpatterns = [
    path("activated", AgreementActivateAPIView.as_view(), name="agreement_activate"),
    path("activated/bulk", AgreementActivateBulkAPIView.as_view(), name="agreement_activate_bulk"),
    path("deactivated", AgreementDeactivateAPIView.as_view(), name="agreement_deactivate"),
    path("deactivated/bulk", AgreementDeactivateBulkAPIView.as_view(), name="agreement_deactivate_bulk"),
    path("copied", AgreementCopyAPIView.as_view(), name="agreements_copy"),
    path("copied/bulk", AgreementBulkCopyAPIView.as_view(), name="agreements_copy_bulk"),
    path("cloned", AgreementCloneAPIView.as_view(), name="agreements_clone"),
    path("cloned/bulk", AgreementBulkCloneAPIView.as_view(), name="agreements_clone_bulk"),
    path("renewed", AgreementRenewAPIView.as_view(), name="agreements_renew"),
    path("change-status/bulk", AgreementBulkStatusChangeAPIView.as_view(), name="agreements_status_change_bulk"),
    path("filters", AgreementFilterParametersAPIView.as_view(), name="agreement_filter_parameters"),
    path("iotron/submitted", SubmitAgreementsToIOTRONAPIView.as_view(), name="submit_agreements_to_iotron"),
    path("", AgreementCollectionAPIView.as_view(), name="agreements"),
    path("xlsx", AgreementListXlsxView.as_view(), name="agreements_xlsx"),
    path(r"<int:agreement_id>", AgreementAPIView.as_view(), name="agreement_parameters"),
    path(r"<int:agreement_id>/intersected", AgreementIntersectedAPIView.as_view(), name="agreement_intersected"),
    path(r"<int:agreement_id>/linked", AgreementLinkedAPIView.as_view(), name="agreement_linked"),
]
