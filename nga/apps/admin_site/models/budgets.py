from itertools import groupby
from typing import Any, Optional, cast

from dependency_injector.wiring import Closing, Provide, inject
from django.contrib import admin, messages
from django.contrib.admin import helpers
from django.contrib.auth.admin import csrf_protect_m
from django.db.models import F, QuerySet, Sum
from django.db.transaction import atomic
from django.http import HttpRequest, HttpResponse, HttpResponseRedirect
from django.templatetags.static import static
from django.urls import reverse
from django.utils.html import format_html
from rangefilter.filters import DateRangeFilter
from tablib import Dataset

from nga.apps.admin_site.filters import (
    AgreementModelAutocompleteFilter,
    BudgetAutocompleteFilter,
    BudgetSnapshotAutocompleteFilter,
    BudgetSubSnapshotAutocompleteFilter,
    CreatedByAutocompleteFilter,
    DiscountAutocompleteFilter,
    DiscountModelAutocompleteFilter,
    ForecastRuleFilter,
    HomeOperatorAutocompleteFilter,
    PartnerOperatorAutocompleteFilter,
    TrafficSegmentAutocompleteFilter,
)
from nga.apps.admin_site.forms import BudgetAdminForm
from nga.apps.admin_site.mixins import AgreementSearchURL, DiscountSearchURL, HomeOperatorsFieldMixin
from nga.apps.admin_site.models.references_traffic import TrafficRecordWithCalledCountryAdminMixin
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.enums import BudgetCalculationStatusEnum, BudgetSnapshotTypeEnum
from nga.apps.budgets.infra.orm import models
from nga.apps.budgets.infra.orm.models import BudgetTrafficRecord
from nga.apps.budgets.infra.repositories import BudgetDjangoORMRepository
from nga.apps.common.consts import HPMN, VPMN
from nga.apps.common.expressions import home_pmn_code, partner_pmn_code
from nga.apps.common.queryset_utils import to_pk_list
from nga.apps.import_export_data.resources.budgets import (
    InboundBudgetTrafficRecordResource,
    OutboundBudgetTrafficRecordResource,
)
from nga.apps.import_export_data.utils import CSVHttpResponseFromDataset, ZipWithCSVFilesHttpResponse
from nga.core.enums import TrafficDirectionEnum, TrafficTypeEnum


@admin.register(models.Budget)
class BudgetAdmin(HomeOperatorsFieldMixin, admin.ModelAdmin):
    form = BudgetAdminForm

    autocomplete_fields = ("home_operators",)
    list_display = (
        "pk",
        "name",
        "description",
        "start_date",
        "end_date",
        "is_master",
        "home_operator_list",
        "last_historical_month",
        "view_values_url",
        "is_deleting",
    )
    sortable_by = list_display
    list_display_links = ("name",)
    list_filter = ("is_master",)
    search_fields = (
        "pk",
        "name",
        "description",
        "home_operators__pmn_code",
        "home_operators__name",
    )
    readonly_fields = (
        "id",
        "created_at",
        "updated_at",
        "forecast_rules_modified_at",
        "historical_traffic_modified_at",
        "agreements_last_modified_at",
        "is_deleting",
    )
    actions = ["export_inbound_budget", "export_outbound_budget"]

    show_facets = admin.ShowFacets.NEVER

    truncate_after = 3

    REMOVE_MASTER_BUDGET_ERROR = "Master Budget removal action is forbidden!"
    MULTIPLE_BUDGETS_EXPORT_ERROR = "Multiple Budgets export is forbidden!"
    EMPTY_BUDGET_EXPORT_WARNING = "Budget has no calculated forecasted volumes!"

    export_csv_filename_template = "{home_operators}_{traffic_direction}_PMN_Forecasted_Volumes.csv"
    export_zip_filename_template = "{traffic_direction}_PMN_Forecasted_Volumes.zip"

    @classmethod
    @admin.display(ordering="_is_deleting")
    def is_deleting(cls, obj: models.Budget) -> bool:
        return obj._is_deleting

    def export_inbound_budget(
        self,
        request: HttpRequest,
        queryset: QuerySet[models.Budget],
    ) -> Optional[HttpResponse]:
        return self._export_budget(request, queryset, TrafficDirectionEnum.INBOUND)

    def export_outbound_budget(
        self,
        request: HttpRequest,
        queryset: QuerySet[models.Budget],
    ) -> Optional[HttpResponse]:
        return self._export_budget(request, queryset, TrafficDirectionEnum.OUTBOUND)

    def _export_budget(
        self,
        request: HttpRequest,
        queryset: QuerySet[models.Budget],
        traffic_direction: TrafficDirectionEnum,
    ) -> Optional[HttpResponse]:
        """Returns BudgetTrafficRecords zip file with split by home operators csv files."""

        budget_id = to_pk_list(queryset)[0]

        qs = (
            BudgetTrafficRecord.objects.filter(
                budget_snapshot__budget_id=budget_id,
                budget_snapshot__type=BudgetSnapshotTypeEnum.ACTIVE,
                traffic_direction=traffic_direction,
                traffic_type=TrafficTypeEnum.FORECASTED,
            )
            .annotate(
                hpmn=home_pmn_code,
                ppmn=partner_pmn_code,
            )
            .values("hpmn", "ppmn", "service_type", "traffic_month")
            .annotate(volume_actual=Sum("volume_actual"))
            .order_by("hpmn", "ppmn", "service_type", "traffic_month")
        )

        records = tuple(qs)
        if not records:
            self._log_empty_budget_export_action(request)
            return None

        csv_responses = []

        for hpmn, h_records in groupby(records, key=lambda x: x["hpmn"]):

            if traffic_direction == TrafficDirectionEnum.INBOUND:
                dataset = InboundBudgetTrafficRecordResource().export(queryset=h_records)
            else:
                dataset = OutboundBudgetTrafficRecordResource().export(queryset=h_records)

            csv_filename = self.get_csv_filename(dataset, traffic_direction)

            csv_responses.append(CSVHttpResponseFromDataset(dataset, csv_filename))

        zip_filename = self.get_zip_filename(traffic_direction)

        response = ZipWithCSVFilesHttpResponse(csv_responses, zip_filename)

        return response

    @classmethod
    def get_csv_filename(cls, dataset: Dataset, traffic_direction: TrafficDirectionEnum) -> str:
        home_operator_field = VPMN if traffic_direction == TrafficDirectionEnum.INBOUND else HPMN
        home_operator_index = dataset.headers.index(home_operator_field)
        home_operators = "_".join(sorted(set(i[home_operator_index] for i in dataset)))

        return cls.export_csv_filename_template.format(
            home_operators=home_operators, traffic_direction=traffic_direction.name
        )

    @classmethod
    def get_zip_filename(cls, traffic_direction: TrafficDirectionEnum) -> str:
        return cls.export_zip_filename_template.format(traffic_direction=traffic_direction.name)

    @classmethod
    def view_values_url(cls, obj: models.Budget) -> str:
        url = reverse("admin:budgets_budgettrafficrecord_changelist") + f"?budget__id={obj.pk}"
        return format_html(f'<a href="{url}">View Traffic Records</a>')

    def get_form(self, request, obj=None, change=False, **kwargs):  # type: ignore[no-untyped-def]
        # master budget must be only one. If master budget is created, there must be no ability
        # to create a new one. If master budget does not exist, user is able to create it.
        master_exists = models.Budget.objects.filter(is_master=True).exists()
        request_for_change = "change" in request.path

        if master_exists or change or request_for_change:
            if "fields" in kwargs and kwargs["fields"] and "is_master" in kwargs["fields"]:
                fields = kwargs["fields"]
                fields.pop(fields.index("is_master"))
                kwargs["fields"] = fields

        form = super().get_form(request, obj, change, **kwargs)

        if master_exists or change or request_for_change:
            if "is_master" in form.base_fields:
                form.base_fields.pop("is_master")

        return form

    def response_action(
        self,
        request: HttpRequest,
        queryset: QuerySet[models.Budget],
    ) -> HttpResponse | None:
        action = request.POST.get("action")

        if action == "delete_selected":  # noqa
            selected_pks = request.POST.getlist(helpers.ACTION_CHECKBOX_NAME)

            if queryset.filter(pk__in=selected_pks, is_master=True).exists():
                self._log_master_budget_removal_error(request)
                return None

        elif action in ("export_inbound_budget", "export_outbound_budget"):
            budgets = request.POST.getlist("_selected_action")
            if len(budgets) > 1:
                self._log_multiple_budgets_export_action(request)
                return None

        return super().response_action(request, queryset)

    def _log_empty_budget_export_action(self, request: HttpRequest) -> None:
        self.message_user(request, self.EMPTY_BUDGET_EXPORT_WARNING, level=messages.WARNING)

    def _log_multiple_budgets_export_action(self, request: HttpRequest) -> None:
        self.message_user(request, self.MULTIPLE_BUDGETS_EXPORT_ERROR, level=messages.ERROR)

    def _log_master_budget_removal_error(self, request: HttpRequest) -> None:
        self.message_user(request, self.REMOVE_MASTER_BUDGET_ERROR, level=messages.ERROR)

    @csrf_protect_m
    def delete_view(
        self,
        request: HttpRequest,
        object_id: str,
        extra_context: Optional[dict[str, Any]] = None,
    ) -> HttpResponse:
        if models.Budget.objects.filter(is_master=True, id=object_id).exists():
            self._log_master_budget_removal_error(request)

            post_url = reverse(
                "admin:%s_%s_changelist" % (self.opts.app_label, self.opts.model_name),
                current_app=self.admin_site.name,
            )
            return HttpResponseRedirect(post_url)

        return super().delete_view(request, object_id, extra_context)

    @inject
    @atomic
    def save_model(
        self,
        request: HttpRequest,
        obj: models.Budget,
        form: Any,
        change: Any,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
    ) -> None:
        """Overrides parent method in order to create snapshots for budget."""

        creating = obj.pk is None

        super().save_model(request, obj, form, change)

        if creating is True:
            if not isinstance(budget_repository, BudgetDjangoORMRepository):
                raise RuntimeError("For Django admin site BudgetDjangoORMRepository must be used!")

            budget_repository.create_default_snapshots(obj, user_id=cast(int, request.user.pk))


@admin.register(models.BudgetTrafficRecord)
class BudgetTrafficRecordAdmin(
    TrafficRecordWithCalledCountryAdminMixin,
    AgreementSearchURL,
    DiscountSearchURL,
    admin.ModelAdmin,
):
    autocomplete_fields = (
        "budget_snapshot",
        "home_operator",
        "partner_operator",
        "called_country",
        "traffic_segment",
        "forecast_rule",
        "discount",
    )
    list_display = (
        "pk",
        "home_operator_pmn",
        "partner_operator_pmn",
        "traffic_month",
        "traffic_type",
        "traffic_direction",
        "service_type",
        "call_destination",
        "called_country_code",
        "is_premium",
        "traffic_segment_name",
        "imsi_count_type",
        "volume_actual",
        "volume_billed",
        "tap_charge_net",
        "tap_charge_gross",
        "charge_net",
        "charge_gross",
    )
    sortable_by = list_display
    list_filter = (
        ("traffic_month", DateRangeFilter),
        BudgetSnapshotAutocompleteFilter,
        "traffic_type",
        "traffic_direction",
        "service_type",
        "call_destination",
        "is_premium",
        "imsi_count_type",
        TrafficSegmentAutocompleteFilter,
        ForecastRuleFilter,
        DiscountAutocompleteFilter,
        HomeOperatorAutocompleteFilter,
        PartnerOperatorAutocompleteFilter,
        AgreementModelAutocompleteFilter,
        DiscountModelAutocompleteFilter,
    )
    search_fields = (
        "pk",
        "budget_snapshot__name",
        "budget_snapshot__budget__name",
        "home_operator__pmn_code",
        "partner_operator__pmn_code",
        "called_country__code",
    )
    readonly_fields = (
        "id",
        "created_at",
        "updated_at",
    )
    list_per_page = 22
    ordering = (
        "home_operator__pmn_code",
        "partner_operator__pmn_code",
        "traffic_month",
    )

    show_facets = admin.ShowFacets.NEVER

    def get_queryset(self, request: HttpRequest) -> QuerySet:
        queryset = super().get_queryset(request)

        queryset = queryset.select_related("budget_snapshot", "forecast_rule")

        return queryset


@admin.register(models.BudgetSnapshot)
class BudgetSnapshotAdmin(admin.ModelAdmin):
    autocomplete_fields = (
        "budget",
        "created_by",
    )
    list_display = (
        "pk",
        "name",
        "budget_id",
        "type",
        "created_by",
        "created_at",
        "updated_at",
    )
    sortable_by = list_display
    list_display_links = ("name",)
    list_filter = (
        BudgetAutocompleteFilter,
        "type",
        CreatedByAutocompleteFilter,
        ("created_at", DateRangeFilter),
    )
    search_fields = (
        "pk",
        "budget__pk",
        "name",
    )

    def has_change_permission(self, request: HttpRequest, obj: Optional[models.BudgetSnapshot] = None) -> bool:
        return False


class BudgetCalculationLogRecordInline(admin.TabularInline):
    model = models.BudgetCalculationLogRecord
    extra = 0
    can_delete = False

    def has_add_permission(self, *args: Any) -> bool:  # pragma: no cover
        return False

    def has_change_permission(self, *args: Any) -> bool:  # pragma: no cover
        return False


@admin.register(models.BudgetCalculation)
class BudgetCalculationAdmin(admin.ModelAdmin):
    autocomplete_fields = (
        "budget_snapshot",
        "created_by",
    )
    list_display = (
        "pk",
        "budget_id",
        "budget_name",
        "type",
        "status",
        "issues_icon",
        "traffic_lhm",
        "distribution_lhm",
        "created_at",
        "finished_at",
        "duration",
        "created_by",
        "traffic_synchronized_at",
        "forecast_rules_applied_at",
        "agreements_applied_at",
    )
    sortable_by = list_display

    list_per_page = 22
    list_select_related = ("budget_snapshot", "budget_snapshot__budget", "created_by")
    list_filter = (
        BudgetSubSnapshotAutocompleteFilter,
        "status",
        "type",
    )

    readonly_fields = (
        "pk",
        "created_at",
        "finished_at",
        "traffic_synchronized_at",
        "forecast_rules_applied_at",
        "agreements_applied_at",
        "job_id",
    )
    search_fields = (
        "pk",
        "budget_snapshot__budget__pk",
        "budget_snapshot__budget__name",
    )
    inlines = (BudgetCalculationLogRecordInline,)

    def get_queryset(self, request: HttpRequest) -> QuerySet[models.BudgetCalculation]:
        """
        Overriden due to solve n+1 query problem. Adds to objects related
        fields needed to be displayed in a table.
        """

        queryset = super().get_queryset(request)

        queryset = queryset.annotate(
            budget_id=F("budget_snapshot__budget__id"),
            budget_name=F("budget_snapshot__budget__name"),
        )

        queryset = queryset.prefetch_related("log_records")

        return queryset

    @classmethod
    @admin.display(ordering="budget_snapshot__budget__name")
    def budget_name(cls, obj: models.BudgetCalculation) -> str:
        return obj.budget_name

    @classmethod
    @admin.display(ordering="budget_snapshot__budget__id")
    def budget_id(cls, obj: models.BudgetCalculation) -> str:
        return obj.budget_id

    @classmethod
    @admin.display(description="")
    def issues_icon(cls, obj: models.BudgetCalculation) -> str:
        """Returns icon based on issues presence in calculation."""

        calculation_has_issues = obj.log_records.exists()

        if obj.status == BudgetCalculationStatusEnum.FAILED:
            icon = "icon-no"
        else:
            icon = "icon-alert" if calculation_has_issues else "icon-yes"

        icon_url = static(f"admin/img/{icon}.svg")

        return format_html("<img src={icon_url}>", icon_url=icon_url)

    @classmethod
    def duration(cls, obj: models.BudgetCalculation) -> str:
        if obj.finished_at is not None:
            return str(obj.finished_at - obj.created_at).split(".")[0]

        return ""
