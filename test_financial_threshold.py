#!/usr/bin/env python3
"""
Quick test script to verify FINANCIAL_THRESHOLD is available in the enum and serializers.
Run this from the Django project root: python test_financial_threshold.py
"""

import os
import sys
import django

# Add the project root to Python path
sys.path.insert(0, '/home/<USER>/projects/nga/nga-api')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nga.settings.local')
django.setup()

from nga.apps.agreements.enums import DiscountBoundTypeEnum
from nga.apps.agreements.api.serializers.discount_create import DiscountParameterCreateSerializer
from nga.apps.common.serializer_fields import EnumChoiceField
from nga.core.enums import get_choices, get_choices_labels

def test_enum():
    print("=== Testing DiscountBoundTypeEnum ===")
    print(f"All enum values: {list(DiscountBoundTypeEnum)}")
    print(f"FINANCIAL_THRESHOLD value: {DiscountBoundTypeEnum.FINANCIAL_THRESHOLD}")
    print(f"FINANCIAL_THRESHOLD name: {DiscountBoundTypeEnum.FINANCIAL_THRESHOLD.name}")
    print()

def test_enum_choices():
    print("=== Testing enum choices functions ===")
    choices = get_choices(DiscountBoundTypeEnum)
    labels = get_choices_labels(DiscountBoundTypeEnum)
    print(f"get_choices(): {choices}")
    print(f"get_choices_labels(): {labels}")
    print(f"FINANCIAL_THRESHOLD in labels: {'FINANCIAL_THRESHOLD' in labels}")
    print()

def test_serializer_field():
    print("=== Testing EnumChoiceField ===")
    field = EnumChoiceField(enum_class=DiscountBoundTypeEnum)
    print(f"Field choices: {field.choices}")
    print(f"Field choices_labels: {field.choices_labels}")
    print(f"FINANCIAL_THRESHOLD in choices_labels: {'FINANCIAL_THRESHOLD' in field.choices_labels}")
    print()

def test_serializer():
    print("=== Testing DiscountParameterCreateSerializer ===")
    serializer = DiscountParameterCreateSerializer()
    bound_type_field = serializer.fields['bound_type']
    print(f"Bound type field: {bound_type_field}")
    print(f"Bound type choices: {bound_type_field.choices}")
    print(f"Bound type choices_labels: {bound_type_field.choices_labels}")
    print(f"FINANCIAL_THRESHOLD in choices_labels: {'FINANCIAL_THRESHOLD' in bound_type_field.choices_labels}")
    print()

def test_serializer_validation():
    print("=== Testing serializer validation ===")
    serializer = DiscountParameterCreateSerializer()
    
    # Test if FINANCIAL_THRESHOLD is accepted
    try:
        field = serializer.fields['bound_type']
        result = field.to_internal_value('FINANCIAL_THRESHOLD')
        print(f"✅ FINANCIAL_THRESHOLD validation successful: {result}")
    except Exception as e:
        print(f"❌ FINANCIAL_THRESHOLD validation failed: {e}")
    
    print()

if __name__ == "__main__":
    print("Testing FINANCIAL_THRESHOLD availability...\n")
    
    test_enum()
    test_enum_choices()
    test_serializer_field()
    test_serializer()
    test_serializer_validation()
    
    print("=== Summary ===")
    print("If FINANCIAL_THRESHOLD appears in all tests above, then the enum is working correctly.")
    print("If it's missing from API responses, the issue might be:")
    print("1. Application server needs restart")
    print("2. Frontend caching")
    print("3. Different environment/database")
    print("4. API endpoint filtering")
