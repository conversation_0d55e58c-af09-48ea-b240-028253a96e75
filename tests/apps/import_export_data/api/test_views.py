from unittest.mock import patch

import pytest
from django.contrib.auth.models import Anonymous<PERSON>ser, User
from rest_framework import status
from rest_framework.response import Response
from rest_framework.reverse import reverse_lazy
from rest_framework.test import force_authenticate

from nga.apps.import_export_data.api.views import ImportDataJobView
from nga.apps.import_export_data.models import ImportDataJob


@pytest.mark.django_db
class TestImportDataJobView:
    view_class = ImportDataJobView
    url = reverse_lazy("run_import_data_job")

    def test_authentication_required(self, rf):
        request = rf.post(self.url, content_type="application/json")
        request.user = AnonymousUser()

        response = self.view_class.as_view()(request)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @patch("nga.apps.import_export_data.api.views.run_import_data_job")
    def test_job_is_started_202(self, task_run_job_mock, rf, staff_user):
        job = ImportDataJob.objects.create(
            file=ImportDataJob.build_file_path("test-file"),
        )

        response = self.run_job(rf, staff_user, {"file_key": "test-file"})

        assert response.status_code == status.HTTP_202_ACCEPTED

        task_run_job_mock.delay.assert_called_once_with(
            instance_pk=job.pk,
            instance_model=job.model,
            dry_run=False,
            run_async=True,
        )

    def test_when_job_does_not_exist_404(self, rf, staff_user):
        response = self.run_job(rf, staff_user, {"file_key": "test-file"})

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def run_job(
        self,
        rf,
        user: User,
        data: dict,
    ) -> Response:
        request = rf.post(self.url, data=data, content_type="application/json")
        force_authenticate(request, user)
        response = self.view_class.as_view()(request)

        return response
