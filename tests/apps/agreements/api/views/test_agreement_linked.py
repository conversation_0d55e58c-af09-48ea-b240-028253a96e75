from django.contrib.auth.models import AnonymousUser
from django.test import RequestFactory
from rest_framework import status
from rest_framework.reverse import reverse_lazy
from rest_framework.test import force_authenticate

from nga.apps.agreements.api.views import AgreementLinkedAPIView
from nga.apps.users.models import User
from nga.utils.collections import to_id_list
from tests.apps.agreements.fakes import InMemoryBudgetAgreementRepository
from tests.apps.budgets.fakes import InMemoryBudgetProvider
from tests.factories.agreements import BudgetAgreementFactory
from tests.factories.budgets import BudgetFactory


class TestAgreementLinkedAPIView:
    view_class = AgreementLinkedAPIView
    url_name = "agreement_linked"

    def test_authentication_required(self, rf: RequestFactory):
        url = reverse_lazy(self.url_name, kwargs={"budget_id": 1, "agreement_id": 1})
        request = rf.get(url)
        request.user = AnonymousUser()

        response = self.view_class.as_view()(request)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_get_200_ok(self, rf, staff_user, override_deps):
        budget = BudgetFactory()
        budget_provider = InMemoryBudgetProvider(budgets=[budget])

        ba_1 = BudgetAgreementFactory(budget_id=budget.id, parent_id=None)
        ba_2 = BudgetAgreementFactory(budget_id=budget.id, parent_id=ba_1.agreement_id)
        ba_3 = BudgetAgreementFactory(budget_id=budget.id, parent_id=ba_2.agreement_id)
        ba_4 = BudgetAgreementFactory(budget_id=budget.id, parent_id=ba_3.agreement_id)

        budget_agreement_repository = InMemoryBudgetAgreementRepository([ba_1, ba_2, ba_3, ba_4])

        with override_deps(
            budget_provider=budget_provider,
            budget_agreement_repository=budget_agreement_repository,
        ):
            response = self.get_agreement_linked(rf, staff_user, budget_id=budget.id, agreement_id=ba_2.id)

        assert response.status_code == status.HTTP_200_OK

        assert sorted([ba["id"] for ba in response.data]) == sorted(to_id_list([ba_1, ba_2, ba_3, ba_4]))

    def test_get_404_when_budget_does_not_exist(self, rf, staff_user, override_deps):
        budget_id, agreement_id = 7789, 78937

        with override_deps(budget_provider=InMemoryBudgetProvider()):
            response = self.get_agreement_linked(rf, staff_user, budget_id=budget_id, agreement_id=agreement_id)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_get_404_when_agreement_does_not_exist(self, rf, staff_user, override_deps):
        budget = BudgetFactory()
        budget_provider = InMemoryBudgetProvider(budgets=[budget])

        agreement_id = 53334

        with override_deps(
            budget_provider=budget_provider,
            budget_agreement_repository=InMemoryBudgetAgreementRepository(),
        ):
            response = self.get_agreement_linked(rf, staff_user, budget.id, agreement_id=agreement_id)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def get_agreement_linked(self, rf: RequestFactory, user: User, budget_id: int, agreement_id: int):
        kwargs = {"budget_id": budget_id, "agreement_id": agreement_id}
        url = reverse_lazy(self.url_name, kwargs=kwargs)
        request = rf.get(url, content_type="application/json")
        request.user = user
        force_authenticate(request, user)

        response = self.view_class.as_view()(request, **kwargs)

        return response
