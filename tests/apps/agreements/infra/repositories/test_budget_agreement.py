from datetime import date, datetime, timezone
from functools import partial

from dateutil.relativedelta import relativedelta

import pytest
from nga.apps.agreements.domain.dto import BudgetAgreementCreateDTO
from nga.apps.agreements.domain.exceptions import BudgetAgreementDoesNotExist
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.enums import AgreementCalculationStatusEnum, AgreementStatusEnum
from nga.apps.agreements.infra.orm import models
from nga.apps.agreements.infra.repositories.budget_agreement import (
    BudgetAgreementDjangoORMRepository,
    from_orm_to_domain,
)
from nga.apps.budgets.domain.dto import BudgetParametersFilters
from nga.apps.common.queryset_utils import to_pk_list
from nga.core.types import DatePeriod
from nga.utils.collections import to_id_list
from nga.utils.dt import get_current_datetime_utc
from tests.factories.agreements import AgreementORMFactory, BudgetAgreementORMFactory
from tests.factories.agreements.orm import (
    AgreementNegotiatorORMFactory,
    DiscountORMFactory,
    DiscountParameterORMFactory,
)
from tests.factories.budgets import BudgetORMFactory
from tests.factories.references import CountryORMFactory, OperatorORMFactory


@pytest.fixture
def _repository() -> BudgetAgreementDjangoORMRepository:
    return BudgetAgreementDjangoORMRepository()


@pytest.mark.django_db
class TestBudgetAgreementORMRepository:
    def test_mapping_get_many(self, _repository):
        home_operators = [OperatorORMFactory().id, OperatorORMFactory().id]
        partner_operators = [OperatorORMFactory().id]

        orm_budget_agreement = BudgetAgreementORMFactory(
            agreement__home_operators=home_operators,
            agreement__partner_operators=partner_operators,
            agreement__include_satellite=False,
            agreement__include_premium=False,
            agreement__include_premium_in_commitment=False,
            agreement__is_rolling=False,
            is_active=True,
        )
        orm_budget = orm_budget_agreement.budget
        orm_agreement = orm_budget_agreement.agreement

        agreements = _repository.get_many(budget_id=orm_budget.id)
        assert len(agreements) == 1
        domain_agreement = agreements[0]

        assert domain_agreement.id == orm_budget_agreement.id
        assert domain_agreement.budget_id == orm_budget.id
        assert domain_agreement.agreement_id == orm_agreement.id
        assert domain_agreement.name == orm_agreement.name
        assert domain_agreement.status == AgreementStatusEnum(orm_agreement.status)
        assert domain_agreement.is_active == orm_budget_agreement.is_active
        assert domain_agreement.home_operators == home_operators
        assert domain_agreement.partner_operators == partner_operators
        assert domain_agreement.period == DatePeriod(
            orm_agreement.start_date,
            orm_agreement.end_date,
        )
        assert domain_agreement.calculation_status == orm_budget_agreement.calculation_status

        assert domain_agreement.include_satellite is False
        assert domain_agreement.include_premium is False
        assert domain_agreement.include_premium_in_commitment is False

        assert domain_agreement.include_premium_in_commitment is False

    def test_get_many_filters_by_budget_id(self, _repository):
        orm_budget_1 = BudgetORMFactory()
        orm_budget_2 = BudgetORMFactory()

        orm_budget_agreement = BudgetAgreementORMFactory(budget=orm_budget_1)

        BudgetAgreementORMFactory(budget=orm_budget_2, agreement=orm_budget_agreement.agreement)

        BudgetAgreementORMFactory()
        BudgetAgreementORMFactory()

        domain_agreements = _repository.get_many(budget_id=orm_budget_1.id)

        assert len(domain_agreements) == 1
        assert to_id_list(domain_agreements) == [orm_budget_agreement.id]

    def test_get_many_filters_by_non_budget_id(self, _repository):
        BudgetAgreementORMFactory()
        BudgetAgreementORMFactory()

        domain_agreements = _repository.get_many(budget_id=-1)

        assert len(domain_agreements) == 0

    def test_filters_by_agreement_ids(self, _repository):
        BudgetAgreementORMFactory.create_batch(size=3)
        orm_budget_agreements = BudgetAgreementORMFactory.create_batch(size=2)

        domain_agreements = _repository.get_many(budget_agreement_ids=to_pk_list(orm_budget_agreements))
        assert to_id_list(domain_agreements) == to_pk_list(orm_budget_agreements)

    def test_filters_by_agreement_ids_when_they_are_empty(self, _repository):
        BudgetAgreementORMFactory.create_batch(size=3)

        domain_agreements = _repository.get_many(budget_agreement_ids=[])

        assert len(domain_agreements) == 0

    def test_with_filter_by_home_operators(self, _repository):
        budget = BudgetORMFactory()
        hpmn = OperatorORMFactory()

        hpmn_agreements = BudgetAgreementORMFactory.create_batch(
            size=2,
            budget=budget,
            agreement__home_operators=[hpmn],
        )

        BudgetAgreementORMFactory.create_batch(size=2, budget=budget)  # noise agreements

        agreements = _repository.get_many(
            budget_id=budget.id, budget_parameters=BudgetParametersFilters(home_operators=[hpmn.id])
        )
        assert len(agreements) == len(hpmn_agreements)

    def test_with_filter_by_start_date(self, _repository):
        budget = BudgetORMFactory()
        hpmn = OperatorORMFactory()

        agreement = AgreementORMFactory(home_operators=[hpmn], start_date=date(2020, 1, 1), end_date=date(2020, 12, 1))
        BudgetAgreementORMFactory.create_batch(  # noise agreements
            size=1,
            budget=budget,
            agreement=agreement,
        )

        agreement = AgreementORMFactory(home_operators=[hpmn], start_date=date(2022, 1, 1), end_date=date(2022, 12, 1))
        hpmn_agreements = BudgetAgreementORMFactory.create_batch(
            size=2,
            budget=budget,
            agreement=agreement,
        )

        agreement = AgreementORMFactory(home_operators=[hpmn], start_date=date(2030, 1, 1), end_date=date(2030, 12, 1))
        hpmn_agreements.extend(
            BudgetAgreementORMFactory.create_batch(
                size=4,
                budget=budget,
                agreement=agreement,
            )
        )

        agreements = _repository.get_many(budget_id=budget.id, start_date=date(2022, 1, 1))
        assert len(agreements) == len(hpmn_agreements)

    def test_with_filter_by_end_date(self, _repository):
        budget = BudgetORMFactory()
        hpmn = OperatorORMFactory()

        agreement = AgreementORMFactory(home_operators=[hpmn], start_date=date(2020, 1, 1), end_date=date(2020, 12, 31))
        hpmn_agreements = BudgetAgreementORMFactory.create_batch(
            size=1,
            budget=budget,
            agreement=agreement,
        )

        agreement = AgreementORMFactory(home_operators=[hpmn], start_date=date(2022, 1, 1), end_date=date(2022, 12, 31))
        hpmn_agreements.extend(
            BudgetAgreementORMFactory.create_batch(
                size=2,
                budget=budget,
                agreement=agreement,
            )
        )

        agreement = AgreementORMFactory(home_operators=[hpmn], start_date=date(2030, 1, 1), end_date=date(2030, 12, 1))
        BudgetAgreementORMFactory.create_batch(  # noise agreements
            size=4,
            budget=budget,
            agreement=agreement,
        )

        agreements = _repository.get_many(budget_id=budget.id, end_date=date(2022, 12, 1))
        assert len(agreements) == len(hpmn_agreements)

    def test_with_filter_by_start_date_and_end_date(self, _repository):
        budget = BudgetORMFactory()
        hpmn = OperatorORMFactory()

        agreement = AgreementORMFactory(home_operators=[hpmn], start_date=date(2020, 1, 1), end_date=date(2020, 12, 31))
        BudgetAgreementORMFactory.create_batch(  # noise agreements
            size=1,
            budget=budget,
            agreement=agreement,
        )

        agreement = AgreementORMFactory(home_operators=[hpmn], start_date=date(2022, 1, 1), end_date=date(2022, 12, 31))
        hpmn_agreements = BudgetAgreementORMFactory.create_batch(
            size=2,
            budget=budget,
            agreement=agreement,
        )

        agreement = AgreementORMFactory(home_operators=[hpmn], start_date=date(2030, 1, 1), end_date=date(2030, 12, 31))
        BudgetAgreementORMFactory.create_batch(  # noise agreements
            size=4,
            budget=budget,
            agreement=agreement,
        )

        agreements = _repository.get_many(budget_id=budget.id, start_date=date(2022, 1, 1), end_date=date(2022, 12, 1))
        assert len(agreements) == len(hpmn_agreements)

    def test_with_filter_by_partner_operators(self, _repository):
        budget = BudgetORMFactory()
        ppmn = OperatorORMFactory()

        ppmn_agreements = BudgetAgreementORMFactory.create_batch(
            size=2,
            budget=budget,
            agreement__partner_operators=[ppmn],
        )

        BudgetAgreementORMFactory.create_batch(size=2, budget=budget)  # noise agreements

        agreements = _repository.get_many(
            budget_id=budget.id, budget_parameters=BudgetParametersFilters(partner_operators=[ppmn.id])
        )

        assert len(agreements) == len(ppmn_agreements)

    def test_with_filter_by_partner_countries(self, _repository):
        budget = BudgetORMFactory()
        country1 = CountryORMFactory()
        country2 = CountryORMFactory()

        expected_agreements = BudgetAgreementORMFactory.create_batch(
            size=2,
            budget=budget,
            agreement__partner_operators=[OperatorORMFactory(country=country1)],
        )
        expected_agreements.append(
            BudgetAgreementORMFactory(
                budget=budget,
                agreement__partner_operators=[OperatorORMFactory(country=country2)],
            )
        )

        BudgetAgreementORMFactory.create_batch(size=2, budget=budget)  # noise agreements

        agreements = _repository.get_many(
            budget_id=budget.id, budget_parameters=BudgetParametersFilters(partner_countries=[country1.id, country2.id])
        )
        assert len(agreements) == len(expected_agreements)

    def test_with_filter_by_home_operators_and_partner_operators_or_countries(self, _repository):
        budget = BudgetORMFactory()
        hpmn = OperatorORMFactory()
        ppmn = OperatorORMFactory()
        country1 = CountryORMFactory()
        country2 = CountryORMFactory()

        expected_agreements = BudgetAgreementORMFactory.create_batch(
            size=3,
            budget=budget,
            agreement__home_operators=[hpmn],
            agreement__partner_operators=[
                ppmn,
                OperatorORMFactory(country=country1),
                OperatorORMFactory(country=country2),
            ],
        )

        expected_agreements.extend(
            BudgetAgreementORMFactory.create_batch(
                size=2,
                budget=budget,
                agreement__home_operators=[hpmn],
                agreement__partner_operators=[
                    ppmn,
                    OperatorORMFactory(country=country1),
                ],
            )
        )

        BudgetAgreementORMFactory.create_batch(
            size=2,
            budget=budget,
            agreement__home_operators=[hpmn],
        )  # noise agreements

        BudgetAgreementORMFactory.create_batch(
            size=2,
            budget=budget,
            agreement__partner_operators=[
                ppmn,
                OperatorORMFactory(country=country1),
                OperatorORMFactory(country=country2),
            ],
        )  # noise agreements

        BudgetAgreementORMFactory.create_batch(size=2, budget=budget)  # noise agreements

        agreements = _repository.get_many(
            budget_id=budget.id,
            budget_parameters=BudgetParametersFilters(
                home_operators=[hpmn.id],
                partner_operators=[ppmn.id],
                partner_countries=[country1.id, country2.id],
            ),
        )
        assert len(agreements) == len(expected_agreements)

    def test_filters_by_status(self, _repository):
        live_ba = BudgetAgreementORMFactory(agreement__status=AgreementStatusEnum.LIVE)
        BudgetAgreementORMFactory(agreement__status=AgreementStatusEnum.DRAFT)

        budget_agreements = _repository.get_many(statuses=[AgreementStatusEnum.LIVE])

        assert to_id_list(budget_agreements) == [live_ba.pk]

    def test_filters_by_agreement_id(self, _repository):
        agreement = AgreementORMFactory()

        ba_1 = BudgetAgreementORMFactory(agreement=agreement)
        ba_2 = BudgetAgreementORMFactory(budget_id=ba_1.budget_id, agreement=agreement)
        ba_3 = BudgetAgreementORMFactory(budget=BudgetORMFactory(), agreement=agreement)

        # noize
        BudgetAgreementORMFactory(budget_id=ba_1.budget_id)
        BudgetAgreementORMFactory()

        budget_agreements = _repository.get_many(agreement_id=agreement.id)

        assert to_id_list(budget_agreements) == [ba_1.pk, ba_2.pk, ba_3.pk]


@pytest.mark.django_db
class TestFilterByOnlyActiveFlag:
    def test_get_only_active_budget_agreements(self, _repository):
        BudgetAgreementORMFactory(is_active=False)
        orm_ba2 = BudgetAgreementORMFactory(is_active=True)
        orm_ba3 = BudgetAgreementORMFactory(is_active=True)
        BudgetAgreementORMFactory(is_active=False)

        budget_agreements = _repository.get_many(only_active=True)

        assert sorted(to_id_list(budget_agreements)) == [orm_ba2.id, orm_ba3.id]


@pytest.mark.django_db
class TestFiltersByOnlyModifiedFlag:
    def test_get_only_modified_budget_agreements(self, _repository):
        # non-modified agreements
        BudgetAgreementORMFactory(
            agreement__created_at=datetime(2023, 12, 1, 1, 1, 1, tzinfo=timezone.utc),
            agreement__updated_at=datetime(2023, 12, 2, 1, 1, 1, tzinfo=timezone.utc),
            applied_at=datetime(2023, 12, 2, 3, 1, 1, tzinfo=timezone.utc),
        )
        BudgetAgreementORMFactory(
            agreement__created_at=datetime(2023, 11, 1, 1, 1, 1, tzinfo=timezone.utc),
            agreement__updated_at=datetime(2023, 12, 1, 1, 1, 1, tzinfo=timezone.utc),
            applied_at=datetime(2023, 12, 1, 2, 1, 1, tzinfo=timezone.utc),
        )

        # updated_at > applied_at
        modified_ba1 = BudgetAgreementORMFactory(
            agreement__created_at=datetime(2023, 11, 2, 1, 1, 1, tzinfo=timezone.utc),
            agreement__updated_at=datetime(2023, 12, 2, 1, 1, 1, tzinfo=timezone.utc),
            applied_at=datetime(2023, 12, 1, 1, 1, 1, tzinfo=timezone.utc),
        )

        modified_ba2 = BudgetAgreementORMFactory(
            agreement__created_at=datetime(2023, 11, 1, 1, 1, 1, tzinfo=timezone.utc),
            agreement__updated_at=datetime(2023, 12, 1, 1, 1, 1, tzinfo=timezone.utc),
            applied_at=None,
        )

        only_modified_budget_agreements = _repository.get_many(only_modified=True)

        assert sorted(to_id_list(only_modified_budget_agreements)) == sorted([modified_ba1.id, modified_ba2.id])


@pytest.mark.django_db
class TestCreateBudgetAgreement:
    def test_create(self, _repository):
        budget = BudgetORMFactory()
        home_operators = OperatorORMFactory.create_batch(size=2)
        partner_operators = OperatorORMFactory.create_batch(size=3)
        agreement_negotiator = AgreementNegotiatorORMFactory()

        agreement_dto = BudgetAgreementCreateDTO(
            name="test-agreement",
            home_operators=[o.id for o in home_operators],
            partner_operators=[o.id for o in partner_operators],
            period=DatePeriod(date(2022, 1, 1), date(2022, 4, 1)),
            negotiator_id=agreement_negotiator.id,
            include_satellite=False,
            include_premium=False,
            include_premium_in_commitment=True,
            is_rolling=False,
        )

        agreement = _repository.create(agreement_dto, budget.id)

        orm_budget_agreement = models.BudgetAgreement.objects.get(pk=agreement.id)

        assert orm_budget_agreement.calculation_status == AgreementCalculationStatusEnum.NOT_APPLIED

        orm_agreement = orm_budget_agreement.agreement
        assert sorted(to_pk_list(orm_agreement.home_operators.all())) == sorted(to_id_list(home_operators))
        assert sorted(to_pk_list(orm_agreement.partner_operators.all())) == sorted(to_id_list(partner_operators))

        assert agreement.id == orm_budget_agreement.id
        assert agreement.budget_id == budget.id
        assert agreement.name == agreement_dto.name
        assert agreement.period == agreement_dto.period
        assert agreement.negotiator_id == agreement_negotiator.id
        assert agreement.include_satellite == agreement_dto.include_satellite
        assert agreement.include_premium == agreement_dto.include_premium
        assert agreement.include_premium_in_commitment == agreement_dto.include_premium_in_commitment
        assert agreement.is_rolling == agreement_dto.is_rolling

        assert agreement.status == AgreementStatusEnum.DRAFT

        assert sorted(agreement.home_operators) == sorted(to_id_list(home_operators))
        assert sorted(agreement.partner_operators) == sorted(to_id_list(partner_operators))

        assert agreement.is_active is False
        assert agreement.applied_at is None
        assert isinstance(agreement.updated_at, datetime)


@pytest.mark.django_db
class TestGetByID:
    def test_get_by_id(self, _repository):
        orm_budget_agreement = BudgetAgreementORMFactory()
        agreement = _repository.get_by_id(orm_budget_agreement.id)

        assert isinstance(agreement, BudgetAgreement)
        assert agreement.id == orm_budget_agreement.id

    def test_get_by_id_when_budget_agreement_does_not_exist(self, _repository):
        with pytest.raises(BudgetAgreementDoesNotExist):
            _repository.get_by_id(budget_agreement_id=44)


@pytest.mark.django_db
class TestGetByExternalID:
    def test_get_by_external_id(self, _repository):
        orm_budget_agreement = BudgetAgreementORMFactory()
        agreement = _repository.get_by_external_id(
            orm_budget_agreement.agreement.external_id,
            orm_budget_agreement.budget_id,
        )

        assert isinstance(agreement, BudgetAgreement)
        assert agreement.id == orm_budget_agreement.id

    def test_get_by_external_id_when_budget_agreement_does_not_exist(self, _repository):
        agreement = _repository.get_by_external_id(12, 2)

        assert agreement is None


@pytest.mark.django_db
class TestGetByParentID:
    def test_get_by_parent_id(self, _repository):
        orm_budget_agreement = BudgetAgreementORMFactory()
        agreement = _repository.get_by_parent_id(
            orm_budget_agreement.agreement.parent_id,
            orm_budget_agreement.budget_id,
        )

        assert isinstance(agreement, BudgetAgreement)
        assert agreement.id == orm_budget_agreement.id

    def test_get_by_parent_id_when_budget_agreement_does_not_exist(self, _repository):
        agreement = _repository.get_by_parent_id(78, 3)

        assert agreement is None


@pytest.mark.django_db
class TestDeleteByID:
    def test_when_agreement_is_in_multiple_budgets(self, _repository):
        orm_agreement = AgreementORMFactory()
        orm_budget_agreement = BudgetAgreementORMFactory(agreement=orm_agreement)

        other_orm_budget_agreements = BudgetAgreementORMFactory.create_batch(size=2, agreement=orm_agreement)

        _repository.delete_by_id(orm_budget_agreement.id)

        assert models.BudgetAgreement.objects.filter(pk=orm_budget_agreement.id).first() is None
        assert models.Agreement.objects.filter(pk=orm_agreement.id).first() is not None

        total_other_agreements = models.BudgetAgreement.objects.filter(
            pk__in=to_pk_list(other_orm_budget_agreements)
        ).count()
        assert total_other_agreements == len(other_orm_budget_agreements)

    def test_when_agreement_is_in_one_budget(self, _repository):
        orm_budget_agreement = BudgetAgreementORMFactory()
        orm_agreement = orm_budget_agreement.agreement

        _repository.delete_by_id(orm_budget_agreement.id)

        assert models.BudgetAgreement.objects.filter(pk=orm_budget_agreement.id).first() is None
        assert models.Agreement.objects.filter(pk=orm_agreement.id).first() is None

    def test_when_agreement_has_discounts(self, _repository):
        orm_budget_agreement = BudgetAgreementORMFactory()

        orm_discount = DiscountORMFactory(agreement=orm_budget_agreement.agreement)
        orm_discount_parameter = DiscountParameterORMFactory(discount=orm_discount)

        _repository.delete_by_id(orm_budget_agreement.id)

        assert models.Agreement.objects.filter(pk=orm_budget_agreement.agreement_id).first() is None
        assert models.Discount.objects.filter(pk=orm_discount.pk).first() is None
        assert models.DiscountParameter.objects.filter(pk=orm_discount_parameter.pk).first() is None


@pytest.mark.django_db
class TestCopyWhenBudgetAgreementIsConfirmed:
    @pytest.mark.parametrize(
        "confirmed_status",
        (
            AgreementStatusEnum.APPROVED,
            AgreementStatusEnum.LIVE,
            AgreementStatusEnum.CLOSED,
            AgreementStatusEnum.SUBMITTED,
        ),
    )
    def test_add_to_budget(
        self,
        # test parameters
        confirmed_status: AgreementStatusEnum,
        # fixtures
        _repository,
    ):
        orm_budget = BudgetORMFactory()
        orm_budget_agreement = BudgetAgreementORMFactory(agreement__status=confirmed_status)

        domain_agreement = from_orm_to_domain(orm_budget_agreement)

        copied_agreement = _repository.copy(domain_agreement, orm_budget.id)

        assert models.BudgetAgreement.objects.filter(pk=copied_agreement.id).exists() is True

        assert models.Agreement.objects.count() == 1
        assert models.BudgetAgreement.objects.count() == 2


@pytest.mark.django_db
class TestCopyWhenAgreementIsUnConfirmed:
    @pytest.mark.parametrize(
        "unconfirmed_status",
        (AgreementStatusEnum.DRAFT, AgreementStatusEnum.IN_REVIEW, AgreementStatusEnum.REJECTED),
    )
    def test_agreement_is_cloned(
        self,
        # test parameters
        unconfirmed_status: AgreementStatusEnum,
        # fixtures
        _repository,
    ):
        orm_target_budget = BudgetORMFactory()
        orm_source_budget = BudgetORMFactory()

        orm_source_budget_agreement = BudgetAgreementORMFactory(
            budget=orm_source_budget, agreement__status=unconfirmed_status
        )
        orm_source_agreement = orm_source_budget_agreement.agreement

        domain_agreement = from_orm_to_domain(orm_source_budget_agreement)

        new_domain_agreement = _repository.copy(domain_agreement, orm_target_budget.id)

        assert models.BudgetAgreement.objects.filter(budget_id=orm_source_budget.id).count() == 1
        assert models.BudgetAgreement.objects.filter(budget_id=orm_target_budget.id).count() == 1

        new_orm_budget_agreement = models.BudgetAgreement.objects.get(pk=new_domain_agreement.id)
        new_orm_agreement: models.Agreement = new_orm_budget_agreement.agreement

        assert new_domain_agreement.id != orm_source_budget_agreement.pk

        assert new_orm_agreement.name == orm_source_agreement.name
        assert new_orm_agreement.status == AgreementStatusEnum.DRAFT
        assert new_orm_agreement.start_date == orm_source_agreement.start_date + relativedelta(day=1)
        assert new_orm_agreement.end_date == orm_source_agreement.end_date + relativedelta(day=31)

        assert list(new_orm_agreement.home_operators.all()) == list(orm_source_agreement.home_operators.all())
        assert list(new_orm_agreement.partner_operators.all()) == list(orm_source_agreement.partner_operators.all())

        assert new_orm_budget_agreement.applied_at is None

    @pytest.mark.parametrize(
        "agreement_status, use_negotiator, expected_negotiator",
        [
            (AgreementStatusEnum.LIVE, True, True),
            (AgreementStatusEnum.LIVE, False, False),
            (AgreementStatusEnum.DRAFT, True, False),
            (AgreementStatusEnum.DRAFT, False, False),
        ],
    )
    def test_agreement_clone_with_negotiator(
        self,
        agreement_status: AgreementStatusEnum,
        use_negotiator: bool,
        expected_negotiator: bool,
        _repository,
    ):
        orm_target_budget = BudgetORMFactory()
        orm_source_budget = BudgetORMFactory()

        agreement_negotiator = AgreementNegotiatorORMFactory()

        orm_source_budget_agreement = BudgetAgreementORMFactory(
            budget=orm_source_budget,
            agreement__status=agreement_status,
            agreement__negotiator=agreement_negotiator if use_negotiator else None,
        )

        domain_agreement = from_orm_to_domain(orm_source_budget_agreement)

        new_domain_agreement = _repository.copy(domain_agreement, orm_target_budget.id)

        assert models.BudgetAgreement.objects.filter(budget_id=orm_source_budget.id).count() == 1
        assert models.BudgetAgreement.objects.filter(budget_id=orm_target_budget.id).count() == 1

        assert domain_agreement.id != new_domain_agreement.id

        expected_negotiator_id = agreement_negotiator.id if expected_negotiator else None

        assert new_domain_agreement.negotiator_id == expected_negotiator_id


@pytest.mark.django_db
class TestUpdateMany:
    def test_update_many_applied_at_field(self, _repository):
        orm_ba1 = BudgetAgreementORMFactory(applied_at=None)
        orm_ba2 = BudgetAgreementORMFactory(applied_at=datetime(2023, 1, 1, 1, 1, 1, tzinfo=timezone.utc))

        ba1 = from_orm_to_domain(orm_ba1)
        ba1.applied_at = datetime(2023, 1, 1, 1, 1, 1, tzinfo=timezone.utc)

        ba2 = from_orm_to_domain(orm_ba2)
        ba2.applied_at = datetime(2023, 12, 1, 1, 1, 1, tzinfo=timezone.utc)

        _repository.update_many([ba1, ba2])

        orm_ba1.refresh_from_db()
        assert orm_ba1.applied_at == ba1.applied_at

        orm_ba2.refresh_from_db()
        assert orm_ba2.applied_at == ba2.applied_at

    def test_update_many_is_active_field(self, _repository):
        orm_ba1 = BudgetAgreementORMFactory(is_active=False)
        orm_ba2 = BudgetAgreementORMFactory(applied_at=None, is_active=True)

        ba1 = from_orm_to_domain(orm_ba1)
        ba1.is_active = True

        ba2 = from_orm_to_domain(orm_ba2)
        ba2.is_active = False
        ba2.applied_at = datetime(2023, 12, 1, 1, 1, 1, tzinfo=timezone.utc)

        _repository.update_many([ba1, ba2])

        orm_ba1.refresh_from_db()
        assert orm_ba1.is_active == ba1.is_active

        orm_ba2.refresh_from_db()
        assert orm_ba2.is_active == ba2.is_active
        assert orm_ba2.applied_at == ba2.applied_at

    def test_update_calculation_status(self, _repository):
        orm_ba1 = BudgetAgreementORMFactory(calculation_status=AgreementCalculationStatusEnum.NOT_APPLIED)
        orm_ba2 = BudgetAgreementORMFactory(calculation_status=AgreementCalculationStatusEnum.NOT_APPLIED)

        ba1 = from_orm_to_domain(orm_ba1)
        ba1.calculation_status = AgreementCalculationStatusEnum.APPLIED

        ba2 = from_orm_to_domain(orm_ba2)
        ba2.calculation_status = AgreementCalculationStatusEnum.FAILED

        _repository.update_many([ba1, ba2])

        orm_ba1.refresh_from_db()
        assert orm_ba1.calculation_status == AgreementCalculationStatusEnum.APPLIED

        orm_ba2.refresh_from_db()
        assert orm_ba2.calculation_status == AgreementCalculationStatusEnum.FAILED


@pytest.mark.django_db
class TestAgreementIntersection:
    def test_when_there_is_no_intersection(self, _repository):
        orm_budget = BudgetORMFactory()

        orm_hpmn = OperatorORMFactory()
        orm_ppmn = OperatorORMFactory()

        agreement = AgreementORMFactory(
            start_date=date(2022, 1, 1),
            end_date=date(2022, 3, 1),
            home_operators=[orm_hpmn],
            partner_operators=[orm_ppmn],
        )

        # noise agreements to make non equal Agreement and BudgetAgreement ID
        AgreementORMFactory.create_batch(size=3)
        BudgetAgreementORMFactory.create_batch(size=4)

        orm_budget_agreement = BudgetAgreementORMFactory(budget=orm_budget, agreement=agreement, is_active=True)

        BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 4, 1),
            agreement__end_date=date(2022, 5, 1),
            agreement__home_operators=[orm_hpmn],
            agreement__partner_operators=[orm_ppmn],
        )
        BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 1, 1),
            agreement__end_date=date(2022, 5, 1),
            agreement__home_operators=[OperatorORMFactory()],
            agreement__partner_operators=[OperatorORMFactory()],
        )
        BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 1, 1),
            agreement__end_date=date(2022, 5, 1),
            agreement__home_operators=[orm_hpmn],
            agreement__partner_operators=[OperatorORMFactory()],
        )
        BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 1, 1),
            agreement__end_date=date(2022, 5, 1),
            agreement__home_operators=[OperatorORMFactory()],
            agreement__partner_operators=[orm_ppmn],
        )

        domain_agreement = from_orm_to_domain(orm_budget_agreement)
        has_intersection = _repository.has_intersection(domain_agreement)

        assert has_intersection is False

    def test_no_intersection_when_there_is_shared_agreement_in_another_budget(self, _repository):
        orm_budget1 = BudgetORMFactory()
        orm_budget2 = BudgetORMFactory()
        orm_hpmn, orm_ppmn = OperatorORMFactory(), OperatorORMFactory()

        orm_agreement_shared = AgreementORMFactory(
            start_date=date(2022, 1, 1),
            end_date=date(2022, 3, 1),
            home_operators=[orm_hpmn],
            partner_operators=[orm_ppmn],
            status=AgreementStatusEnum.DRAFT,
        )

        BudgetAgreementORMFactory(agreement=orm_agreement_shared, budget=orm_budget1, is_active=False)
        BudgetAgreementORMFactory(agreement=orm_agreement_shared, budget=orm_budget2, is_active=True)

        checked_orm_budget_agreement = BudgetAgreementORMFactory(
            budget=orm_budget1,
            agreement__start_date=date(2022, 1, 1),
            agreement__end_date=date(2022, 3, 1),
            agreement__home_operators=[orm_hpmn],
            agreement__partner_operators=[orm_ppmn],
            agreement__status=AgreementStatusEnum.DRAFT,
            is_active=False,
        )

        domain_agreement = from_orm_to_domain(checked_orm_budget_agreement)

        has_intersection = _repository.has_intersection(domain_agreement, with_active=True)

        assert has_intersection is False

    def test_intersection_with_statuses(self, _repository):
        orm_budget = BudgetORMFactory()
        orm_hpmn, orm_ppmn = OperatorORMFactory(), OperatorORMFactory()

        BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 1, 1),
            agreement__end_date=date(2022, 3, 1),
            agreement__home_operators=[orm_hpmn],
            agreement__partner_operators=[orm_ppmn],
            agreement__status=AgreementStatusEnum.LIVE,
            is_active=True,
        )
        orm_budget_agreement = BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 1, 1),
            agreement__end_date=date(2022, 3, 1),
            agreement__home_operators=[orm_hpmn],
            agreement__partner_operators=[orm_ppmn],
            agreement__status=AgreementStatusEnum.LIVE,
            is_active=True,
        )

        domain_agreement = from_orm_to_domain(orm_budget_agreement)

        has_intersection = _repository.has_intersection(domain_agreement, with_statuses=[AgreementStatusEnum.LIVE])

        assert has_intersection is True

    def test_no_intersection_when_statuses_specified(self, _repository):
        orm_budget = BudgetORMFactory()
        orm_hpmn, orm_ppmn = OperatorORMFactory(), OperatorORMFactory()

        BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 1, 1),
            agreement__end_date=date(2022, 3, 1),
            agreement__home_operators=[orm_hpmn],
            agreement__partner_operators=[orm_ppmn],
            agreement__status=AgreementStatusEnum.APPROVED,
            is_active=True,
        )
        orm_budget_agreement = BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 1, 1),
            agreement__end_date=date(2022, 3, 1),
            agreement__home_operators=[orm_hpmn],
            agreement__partner_operators=[orm_ppmn],
            agreement__status=AgreementStatusEnum.LIVE,
            is_active=True,
        )

        domain_agreement = from_orm_to_domain(orm_budget_agreement)

        has_intersection = _repository.has_intersection(domain_agreement, with_statuses=[AgreementStatusEnum.LIVE])

        assert has_intersection is False

    def test_intersection_within_active_agreements(self, _repository):
        orm_budget = BudgetORMFactory()
        orm_hpmn, orm_ppmn = OperatorORMFactory(), OperatorORMFactory()

        BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 1, 1),
            agreement__end_date=date(2022, 3, 1),
            agreement__home_operators=[orm_hpmn],
            agreement__partner_operators=[orm_ppmn],
            agreement__status=AgreementStatusEnum.APPROVED,
            is_active=False,
        )
        orm_budget_agreement = BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 1, 1),
            agreement__end_date=date(2022, 3, 1),
            agreement__home_operators=[orm_hpmn],
            agreement__partner_operators=[orm_ppmn],
            agreement__status=AgreementStatusEnum.LIVE,
            is_active=True,
        )

        domain_agreement = from_orm_to_domain(orm_budget_agreement)

        has_intersection = _repository.has_intersection(domain_agreement, with_active=True)

        assert has_intersection is False

    def test_intersection_within_active_and_inactive_agreements(self, _repository):
        orm_budget = BudgetORMFactory()
        orm_hpmn, orm_ppmn = OperatorORMFactory(), OperatorORMFactory()

        BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 1, 1),
            agreement__end_date=date(2022, 3, 1),
            agreement__home_operators=[orm_hpmn],
            agreement__partner_operators=[orm_ppmn],
            agreement__status=AgreementStatusEnum.APPROVED,
            is_active=False,
        )
        orm_budget_agreement = BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 1, 1),
            agreement__end_date=date(2022, 3, 1),
            agreement__home_operators=[orm_hpmn],
            agreement__partner_operators=[orm_ppmn],
            agreement__status=AgreementStatusEnum.LIVE,
            is_active=True,
        )

        domain_agreement = from_orm_to_domain(orm_budget_agreement)

        has_intersection = _repository.has_intersection(domain_agreement)

        assert has_intersection is True


@pytest.mark.django_db
class TestGetLinkedAgreements:
    def test_get_linked_parents(self, _repository):
        orm_budget = BudgetORMFactory()

        orm_ba_1 = BudgetAgreementORMFactory(budget_id=orm_budget.id, agreement__parent_id=None)
        orm_ba_2 = BudgetAgreementORMFactory(budget_id=orm_budget.id, agreement__parent_id=orm_ba_1.agreement.id)
        orm_ba_3 = BudgetAgreementORMFactory(budget_id=orm_budget.id, agreement__parent_id=orm_ba_2.agreement.id)
        orm_ba_4 = BudgetAgreementORMFactory(budget_id=orm_budget.id, agreement__parent_id=orm_ba_3.agreement.id)

        # Noize
        BudgetAgreementORMFactory(agreement=orm_ba_3.agreement)
        BudgetAgreementORMFactory(agreement=orm_ba_2.agreement)
        BudgetAgreementORMFactory(agreement=orm_ba_1.agreement)

        linked_parent_records = _repository.get_linked_parents(
            budget_agreement_id=orm_ba_1.id,
            budget_id=orm_budget.id,
        )

        assert sorted(to_id_list(linked_parent_records)) == sorted(to_id_list([orm_ba_2, orm_ba_3, orm_ba_4]))

    def test_get_linked_subsidiaries(self, _repository):
        orm_budget = BudgetORMFactory()

        orm_ba_1 = BudgetAgreementORMFactory(budget_id=orm_budget.id, agreement__parent_id=None)
        orm_ba_2 = BudgetAgreementORMFactory(budget_id=orm_budget.id, agreement__parent_id=orm_ba_1.agreement.id)
        orm_ba_3 = BudgetAgreementORMFactory(budget_id=orm_budget.id, agreement__parent_id=orm_ba_2.agreement.id)
        orm_ba_4 = BudgetAgreementORMFactory(budget_id=orm_budget.id, agreement__parent_id=orm_ba_3.agreement.id)

        # Noize
        BudgetAgreementORMFactory(agreement=orm_ba_3.agreement)
        BudgetAgreementORMFactory(agreement=orm_ba_2.agreement)
        BudgetAgreementORMFactory(agreement=orm_ba_1.agreement)

        linked_subsidiaries_records = _repository.get_linked_subsidiaries(
            budget_agreement_id=orm_ba_4.id,
            budget_id=orm_budget.id,
        )

        assert sorted(to_id_list(linked_subsidiaries_records)) == sorted(to_id_list([orm_ba_1, orm_ba_2, orm_ba_3]))


@pytest.mark.django_db
class TestGetIntersectedMany:
    def test_when_there_are_no_intersected_records(self, _repository):
        orm_budget = BudgetORMFactory()

        orm_hpmn = OperatorORMFactory()
        orm_ppmn = OperatorORMFactory()

        home_operators_list = [orm_hpmn.id]
        partner_operators_list = [orm_hpmn.id]
        period = DatePeriod(start_date=date(2022, 1, 1), end_date=date(2022, 3, 1))

        # noise agreements to make non equal Agreement
        AgreementORMFactory.create_batch(size=3)
        BudgetAgreementORMFactory.create_batch(size=4)

        BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 4, 1),
            agreement__end_date=date(2022, 5, 1),
            agreement__home_operators=[orm_hpmn],
            agreement__partner_operators=[orm_ppmn],
        )
        BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 1, 1),
            agreement__end_date=date(2022, 5, 1),
            agreement__home_operators=[OperatorORMFactory()],
            agreement__partner_operators=[OperatorORMFactory()],
        )
        BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 1, 1),
            agreement__end_date=date(2022, 5, 1),
            agreement__home_operators=[orm_hpmn],
            agreement__partner_operators=[OperatorORMFactory()],
        )
        BudgetAgreementORMFactory(
            budget=orm_budget,
            agreement__start_date=date(2022, 1, 1),
            agreement__end_date=date(2022, 5, 1),
            agreement__home_operators=[OperatorORMFactory()],
            agreement__partner_operators=[orm_ppmn],
        )

        intersected_records = _repository.get_intersected_many(
            budget_id=orm_budget.id,
            home_operators=home_operators_list,
            partner_operators=partner_operators_list,
            period=period,
        )

        assert intersected_records == tuple()

    @pytest.mark.parametrize(
        "period",
        [
            pytest.param(
                DatePeriod(start_date=date(2022, 4, 1), end_date=date(2022, 6, 30)), id="test_intersects_in_middle"
            ),
            pytest.param(
                DatePeriod(start_date=date(2022, 6, 1), end_date=date(2023, 9, 30)), id="test_intersects_from_right"
            ),
            pytest.param(
                DatePeriod(start_date=date(2021, 1, 1), end_date=date(2022, 5, 31)), id="test_intersects_from_left"
            ),
            pytest.param(
                DatePeriod(start_date=date(2021, 1, 1), end_date=date(2023, 5, 31)),
                id="test_intersects_over_full_period",
            ),
        ],
    )
    def test_get_intersected_many_by_period(self, _repository, period: DatePeriod):
        orm_budget = BudgetORMFactory()

        orm_hpmn = OperatorORMFactory()
        orm_ppmn = OperatorORMFactory()

        home_operators_list = [orm_hpmn.id]
        partner_operators_list = [orm_ppmn.id]

        # noise agreements to make non equal Agreement
        BudgetAgreementORMFactory.create_batch(size=7)

        orm_record = BudgetAgreementORMFactory(
            budget_id=orm_budget.id,
            agreement__start_date=date(2022, 4, 1),
            agreement__end_date=date(2022, 7, 31),
            agreement__home_operators=[orm_hpmn],
            agreement__partner_operators=[orm_ppmn],
        )

        intersected_records = _repository.get_intersected_many(
            budget_id=orm_budget.id,
            home_operators=home_operators_list,
            partner_operators=partner_operators_list,
            period=period,
        )

        assert len(intersected_records) == 1
        assert intersected_records[0].id == orm_record.id


@pytest.mark.django_db
class TestCount:
    def test_count_without_filters(self, _repository):
        budget = BudgetORMFactory()
        budget_agreements = BudgetAgreementORMFactory.create_batch(size=3, budget=budget)

        BudgetAgreementORMFactory.create_batch(size=3)  # noise agreements

        total_agreements = _repository.count(budget.id)
        assert total_agreements == len(budget_agreements)

    def test_with_filter_by_home_operators(self, _repository):
        budget = BudgetORMFactory()
        hpmn = OperatorORMFactory()

        hpmn_agreements = BudgetAgreementORMFactory.create_batch(
            size=2,
            budget=budget,
            agreement__home_operators=[hpmn],
        )

        BudgetAgreementORMFactory.create_batch(size=2, budget=budget)  # noise agreements

        total_agreements = _repository.count(
            budget.id,
            budget_parameters=BudgetParametersFilters(home_operators=[hpmn.id]),
        )
        assert total_agreements == len(hpmn_agreements)

    def test_with_filter_by_partner_operators(self, _repository):
        budget = BudgetORMFactory()
        ppmn = OperatorORMFactory()

        ppmn_agreements = BudgetAgreementORMFactory.create_batch(
            size=2,
            budget=budget,
            agreement__partner_operators=[ppmn],
        )

        BudgetAgreementORMFactory.create_batch(size=2, budget=budget)  # noise agreements

        total_agreements = _repository.count(
            budget.id,
            budget_parameters=BudgetParametersFilters(partner_operators=[ppmn.id]),
        )
        assert total_agreements == len(ppmn_agreements)

    def test_with_filter_by_partner_countries(self, _repository):
        budget = BudgetORMFactory()
        country1 = CountryORMFactory()
        country2 = CountryORMFactory()

        BudgetAgreementORMFactory.create_batch(
            size=2,
            budget=budget,
            agreement__partner_operators=[OperatorORMFactory(country=country1)],
        )
        BudgetAgreementORMFactory(
            budget=budget,
            agreement__partner_operators=[OperatorORMFactory(country=country2)],
        )

        BudgetAgreementORMFactory.create_batch(size=2, budget=budget)  # noise agreements

        total_agreements = _repository.count(
            budget.id,
            budget_parameters=BudgetParametersFilters(partner_countries=[country1.id, country2.id]),
        )
        assert total_agreements == 3

    def test_with_filter_by_home_operators_and_partner_operators_or_countries(self, _repository):
        budget = BudgetORMFactory()
        hpmn = OperatorORMFactory()
        ppmn = OperatorORMFactory()
        country1 = CountryORMFactory()
        country2 = CountryORMFactory()

        BudgetAgreementORMFactory.create_batch(
            size=3,
            budget=budget,
            agreement__home_operators=[hpmn],
            agreement__partner_operators=[
                ppmn,
                OperatorORMFactory(country=country1),
                OperatorORMFactory(country=country2),
            ],
        )

        BudgetAgreementORMFactory.create_batch(
            size=2,
            budget=budget,
            agreement__home_operators=[hpmn],
            agreement__partner_operators=[
                ppmn,
                OperatorORMFactory(country=country1),
            ],
        )

        BudgetAgreementORMFactory.create_batch(
            size=2,
            budget=budget,
            agreement__home_operators=[hpmn],
        )  # noise agreements

        BudgetAgreementORMFactory.create_batch(
            size=2,
            budget=budget,
            agreement__partner_operators=[
                ppmn,
                OperatorORMFactory(country=country1),
                OperatorORMFactory(country=country2),
            ],
        )  # noise agreements

        BudgetAgreementORMFactory.create_batch(size=2, budget=budget)  # noise agreements

        total_agreements = _repository.count(
            budget.id,
            budget_parameters=BudgetParametersFilters(
                home_operators=[hpmn.id],
                partner_operators=[ppmn.id],
                partner_countries=[country1.id, country2.id],
            ),
        )
        assert total_agreements == 5

    def test_with_period(self, _repository):
        budget = BudgetORMFactory()

        _factory = partial(BudgetAgreementORMFactory, budget_id=budget.id)

        # excluded from period
        _factory(agreement__start_date=date(2024, 1, 1), agreement__end_date=date(2024, 1, 1))
        _factory(agreement__start_date=date(2024, 4, 1), agreement__end_date=date(2024, 10, 1))

        # included in period
        _factory(agreement__start_date=date(2024, 1, 1), agreement__end_date=date(2024, 2, 1))
        _factory(agreement__start_date=date(2024, 2, 1), agreement__end_date=date(2024, 3, 1))
        _factory(agreement__start_date=date(2024, 2, 1), agreement__end_date=date(2024, 2, 1))
        _factory(agreement__start_date=date(2024, 3, 1), agreement__end_date=date(2024, 4, 1))
        _factory(agreement__start_date=date(2024, 1, 1), agreement__end_date=date(2024, 5, 1))

        total_agreements = _repository.count(
            budget.id,
            budget_parameters=BudgetParametersFilters(
                period=DatePeriod(date(2024, 2, 1), date(2024, 3, 1)),
            ),
        )

        assert total_agreements == 5

    def test_with_is_active(self, _repository):
        budget = BudgetORMFactory()

        _factory = partial(BudgetAgreementORMFactory, budget_id=budget.id)

        # is_active is True
        _factory(is_active=True)
        _factory(is_active=True)

        # is_active is False
        _factory(is_active=False)
        _factory(is_active=False)
        _factory(is_active=False)

        active_agreements = _repository.count(budget.id, is_active=True)

        assert active_agreements == 2

        non_active_agreements = _repository.count(budget.id, is_active=False)

        assert non_active_agreements == 3


@pytest.mark.django_db
class TestSave:
    @pytest.mark.parametrize(
        "initial_value,updated_value",
        [
            (False, True),
            (False, False),
            (True, False),
            (True, True),
        ],
    )
    def test_update_is_active(
        self,
        # test parameters
        initial_value: bool,
        updated_value: bool,
        # fixtures
        _repository,
    ):
        orm_budget_agreement = BudgetAgreementORMFactory(is_active=initial_value)

        agreement = from_orm_to_domain(orm_budget_agreement)
        agreement.is_active = updated_value

        updated_agreement = _repository.save(agreement)
        assert updated_agreement.is_active == updated_value

        orm_budget_agreement.refresh_from_db()
        assert orm_budget_agreement.is_active == updated_value

    def test_update_applied_at(self, _repository):
        orm_budget_agreement = BudgetAgreementORMFactory(applied_at=get_current_datetime_utc())

        budget_agreement = from_orm_to_domain(orm_budget_agreement)
        budget_agreement.applied_at = None

        _repository.save(budget_agreement)

        orm_budget_agreement.refresh_from_db()
        assert orm_budget_agreement.applied_at is None

    def test_update_updated_at(self, _repository):
        orm_budget_agreement = BudgetAgreementORMFactory()

        dt = get_current_datetime_utc()

        domain_budget_agreement = from_orm_to_domain(orm_budget_agreement)
        domain_budget_agreement.updated_at = dt

        _repository.save(domain_budget_agreement)

        orm_budget_agreement.agreement.refresh_from_db()

        assert orm_budget_agreement.agreement.updated_at == dt

    def test_update_name(self, _repository):
        agreement_name = "Test Agreement 1122"

        orm_budget_agreement = BudgetAgreementORMFactory()

        domain_budget_agreement = from_orm_to_domain(orm_budget_agreement)
        domain_budget_agreement.name = agreement_name

        updated_agreement = _repository.save(domain_budget_agreement)
        assert updated_agreement.name == agreement_name

        orm_budget_agreement.agreement.refresh_from_db()
        assert orm_budget_agreement.agreement.name == agreement_name

    def test_update_status(self, _repository):
        agreement_status = AgreementStatusEnum.DRAFT

        orm_budget_agreement = BudgetAgreementORMFactory(agreement=AgreementORMFactory(status=AgreementStatusEnum.LIVE))

        domain_budget_agreement = from_orm_to_domain(orm_budget_agreement)
        domain_budget_agreement.status = agreement_status

        updated_agreement = _repository.save(domain_budget_agreement)
        assert updated_agreement.status == agreement_status

        orm_budget_agreement.agreement.refresh_from_db()
        assert orm_budget_agreement.agreement.status == agreement_status

    def test_update_calculation_status(self, _repository):
        orm_budget_agreement = BudgetAgreementORMFactory(calculation_status=AgreementCalculationStatusEnum.NOT_APPLIED)

        domain_budget_agreement = from_orm_to_domain(orm_budget_agreement)

        domain_budget_agreement.calculation_status = AgreementCalculationStatusEnum.FAILED

        updated_domain_budget_agreement = _repository.save(domain_budget_agreement)

        assert updated_domain_budget_agreement.calculation_status == AgreementCalculationStatusEnum.FAILED

    def test_update_period(self, _repository):
        agreement_period = DatePeriod(date(2024, 2, 1), date(2024, 3, 1))

        orm_budget_agreement = BudgetAgreementORMFactory(
            agreement__start_date=date(2012, 1, 1),
            agreement__end_date=date(2012, 5, 1),
        )

        domain_budget_agreement = from_orm_to_domain(orm_budget_agreement)
        domain_budget_agreement.period = agreement_period

        updated_agreement = _repository.save(domain_budget_agreement)
        assert updated_agreement.period == agreement_period

        orm_budget_agreement.agreement.refresh_from_db()
        assert orm_budget_agreement.agreement.start_date == agreement_period.start_date
        assert orm_budget_agreement.agreement.end_date == agreement_period.end_date

    def test_update_home_operators(self, _repository):
        home_operators = [OperatorORMFactory(pmn_code="HPMN1"), OperatorORMFactory(pmn_code="HPMN2")]
        hpmn_list = to_pk_list(home_operators)

        orm_budget_agreement = BudgetAgreementORMFactory()

        domain_budget_agreement = from_orm_to_domain(orm_budget_agreement)
        domain_budget_agreement.home_operators = hpmn_list

        updated_agreement = _repository.save(domain_budget_agreement)
        assert sorted(updated_agreement.home_operators) == sorted(hpmn_list)

        orm_budget_agreement.agreement.refresh_from_db()
        assert sorted(to_pk_list(orm_budget_agreement.agreement.home_operators.all())) == sorted(hpmn_list)

    def test_update_partner_operators(self, _repository):
        partner_operators = [OperatorORMFactory(pmn_code="PPMN1"), OperatorORMFactory(pmn_code="PPMN2")]
        expected_ppmn_list = to_pk_list(partner_operators)

        orm_budget_agreement = BudgetAgreementORMFactory()

        domain_budget_agreement = from_orm_to_domain(orm_budget_agreement)
        domain_budget_agreement.partner_operators = expected_ppmn_list

        updated_agreement = _repository.save(domain_budget_agreement)
        assert sorted(updated_agreement.partner_operators) == sorted(expected_ppmn_list)

        orm_budget_agreement.agreement.refresh_from_db()

        actual_ppmn_list = to_pk_list(orm_budget_agreement.agreement.partner_operators.all())

        assert sorted(actual_ppmn_list) == sorted(expected_ppmn_list)

    @pytest.mark.parametrize(
        "field",
        ("include_satellite", "include_premium", "include_premium_in_commitment"),
    )
    def test_update_include_satellite_and_premium_fields(self, _repository, field: str):
        field_value = False

        orm_budget_agreement = BudgetAgreementORMFactory()
        setattr(orm_budget_agreement.agreement, field, True)

        domain_budget_agreement = from_orm_to_domain(orm_budget_agreement)
        setattr(domain_budget_agreement, field, field_value)

        updated_agreement = _repository.save(domain_budget_agreement)
        assert getattr(updated_agreement, field) == field_value

        orm_budget_agreement.agreement.refresh_from_db()
        assert getattr(orm_budget_agreement.agreement, field) == field_value

    def test_update_is_rolling_field(self, _repository):
        is_rolling_value = False

        orm_budget_agreement = BudgetAgreementORMFactory()
        orm_budget_agreement.agreement.is_rolling = True

        domain_budget_agreement = from_orm_to_domain(orm_budget_agreement)
        domain_budget_agreement.is_rolling = is_rolling_value

        updated_agreement = _repository.save(domain_budget_agreement)
        assert updated_agreement.is_rolling == is_rolling_value

        orm_budget_agreement.agreement.refresh_from_db()
        assert orm_budget_agreement.agreement.is_rolling == is_rolling_value

    def test_update_external_id(self, _repository):
        orm_budget_agreement = BudgetAgreementORMFactory(agreement__external_id=None)

        external_id = 341

        domain_budget_agreement = from_orm_to_domain(orm_budget_agreement)
        domain_budget_agreement.external_id = external_id

        updated_agreement = _repository.save(domain_budget_agreement)
        assert updated_agreement.external_id == external_id

    def test_update_parent_id(self, _repository):
        parent_agreement = AgreementORMFactory()

        orm_budget_agreement = BudgetAgreementORMFactory(agreement__parent_id=None)

        domain_budget_agreement = from_orm_to_domain(orm_budget_agreement)
        domain_budget_agreement.parent_id = parent_agreement.id

        updated_agreement = _repository.save(domain_budget_agreement)
        assert updated_agreement.parent_id == parent_agreement.id
