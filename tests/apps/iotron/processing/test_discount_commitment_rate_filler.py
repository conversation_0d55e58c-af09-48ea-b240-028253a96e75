from datetime import date
from decimal import Decimal
from functools import partial

from nga.apps.agreements.enums import (
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountModelTypeEnum,
)
from nga.apps.iotron.processing.discount_commitment_rate_filler import DiscountAboveCommitmentRateFiller
from nga.core.enums import CallDestinationEnum, ServiceTypeEnum
from nga.core.types import DatePeriod
from tests.apps.agreements.fakes import InMemoryDiscountRepository
from tests.factories.agreements.domain import (
    SREDiscountFactory,
    SREDiscountParameterFactory,
    SteppedTieredDiscountParameterFactory,
)


class TestDiscountAboveCommitmentRateFiller:
    resolver_cls = DiscountAboveCommitmentRateFiller

    home_operators = (7, 8)
    partner_operators = (9, 10)
    period = DatePeriod(date(2025, 1, 1), date(2025, 10, 1))
    service_types = (ServiceTypeEnum.VOICE_MO,)
    direction = DiscountDirectionEnum.BIDIRECTIONAL

    discount_factory = staticmethod(
        partial(
            SREDiscountFactory,
            home_operators=home_operators,
            partner_operators=partner_operators,
            direction=direction,
            service_types=service_types,
            period=period,
            traffic_segments=tuple(),
            above_commitment_rate=None,
            model_type=None,
        )
    )

    def test_fill_above_commitment_rate(self):
        expected_above_commitment_rate = Decimal("0.007")

        parent_discount = SREDiscountFactory()

        sub_discount_1 = self.discount_factory(
            parent_id=parent_discount.id,
            call_destinations=(CallDestinationEnum.HOME, CallDestinationEnum.LOCAL),
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=None,
                    basis_value=Decimal("0.1"),
                )
            ],
        )

        sub_discount_2 = self.discount_factory(
            parent_id=parent_discount.id,
            call_destinations=(CallDestinationEnum.INTERNATIONAL,),
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=None,
                    basis_value=Decimal("0.15"),
                )
            ],
        )

        sub_discount_3 = self.discount_factory(
            parent_id=parent_discount.id,
            call_destinations=tuple(),
            parameters=[
                SteppedTieredDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_COMMITMENT,
                    basis_value=expected_above_commitment_rate,
                )
            ],
        )

        # Non similar discount
        sub_discount_4 = self.discount_factory(
            parent_id=parent_discount.id,
            period=DatePeriod(date(2024, 10, 1), date(2025, 10, 1)),
            service_types=(ServiceTypeEnum.DATA,),
        )

        parent_discount.sub_discounts = tuple([sub_discount_1, sub_discount_2, sub_discount_3, sub_discount_4])

        discount_repository = InMemoryDiscountRepository([parent_discount, *parent_discount.sub_discounts])

        above_commitment_rate_filler = DiscountAboveCommitmentRateFiller(discount_repository)

        above_commitment_rate_filler.fill_above_commitment_rate(parent_discount)

        filled_discount_1 = discount_repository.get_by_id(sub_discount_1.id)
        assert len(filled_discount_1.parameters) == 1
        assert filled_discount_1.above_commitment_rate == expected_above_commitment_rate

        filled_discount_2 = discount_repository.get_by_id(sub_discount_2.id)
        assert len(filled_discount_2.parameters) == 1
        assert filled_discount_2.above_commitment_rate == expected_above_commitment_rate

        try:
            discount_repository.get_by_id(sub_discount_3.id)
        except Exception as e:
            assert f"Discount does not exist id={sub_discount_3.id}" == str(e)

        filled_discount_4 = discount_repository.get_by_id(sub_discount_4.id)
        assert filled_discount_4.above_commitment_rate is None

        parent_discount = discount_repository.get_by_id(parent_discount.id)
        assert parent_discount.above_commitment_rate is None

    def test_fill_above_commitment_rate_sub_discount_with_a_few_parameters(self):
        expected_above_commitment_rate = Decimal("0.007")

        parent_discount = SREDiscountFactory()

        sub_discount_1 = self.discount_factory(
            call_destinations=(CallDestinationEnum.HOME, CallDestinationEnum.LOCAL),
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=None,
                    basis_value=Decimal("0.1"),
                )
            ],
        )

        sub_discount_2 = self.discount_factory(
            call_destinations=tuple(),
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=None,
                    basis_value=Decimal("0.2"),
                ),
                SteppedTieredDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_COMMITMENT,
                    basis_value=expected_above_commitment_rate,
                ),
            ],
        )

        parent_discount.sub_discounts = tuple([sub_discount_1, sub_discount_2])

        discount_repository = InMemoryDiscountRepository([parent_discount, *parent_discount.sub_discounts])

        above_commitment_rate_filler = DiscountAboveCommitmentRateFiller(discount_repository)

        above_commitment_rate_filler.fill_above_commitment_rate(parent_discount)

        filled_discount_1 = discount_repository.get_by_id(sub_discount_1.id)
        assert len(filled_discount_1.parameters) == 1
        assert filled_discount_1.above_commitment_rate == expected_above_commitment_rate

        filled_discount_2 = discount_repository.get_by_id(sub_discount_2.id)

        assert len(filled_discount_2.parameters) == 1

        assert filled_discount_2.parameters[0].basis_value == Decimal("0.2")
        assert filled_discount_2.parameters[0].bound_type is None
        assert filled_discount_2.parameters[0].calculation_type == DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE

        assert filled_discount_2.above_commitment_rate == expected_above_commitment_rate

        assert filled_discount_2.model_type == DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE

        parent_discount = discount_repository.get_by_id(parent_discount.id)
        assert parent_discount.above_commitment_rate is None
