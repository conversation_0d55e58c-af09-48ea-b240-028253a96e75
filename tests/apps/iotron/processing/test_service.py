from copy import copy
from datetime import date
from decimal import Decimal
from functools import partial
from typing import Optional
from unittest.mock import Mock, patch

import pytest
from mediatr import Mediator

from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository, AbstractDiscountRepository
from nga.apps.agreements.domain.services import BudgetAgreementRenewService
from nga.apps.agreements.enums import (
    AgreementCalculationStatusEnum,
    AgreementStatusEnum,
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountModelTypeEnum,
    DiscountQualifyingBasisEnum,
    DiscountSettlementMethodEnum,
    TaxTypeEnum,
)
from nga.apps.agreements.infra.orm import models
from nga.apps.agreements.infra.repositories import (
    AgreementNegotiatorDjangoORMRepository,
    BudgetAgreementDjangoORMRepository,
    DiscountDjangoORMRepository,
)
from nga.apps.budgets.infra.orm.models import Budget
from nga.apps.common.queryset_utils import to_pk_list
from nga.apps.iotron.domain.dto import DiscountImportDict
from nga.apps.iotron.enums import ExternalAgreementProcessingStatusEnum
from nga.apps.iotron.infra.orm.models import ExternalAgreement
from nga.apps.iotron.infra.repositories.external_agreement import ExternalAgreementDjangoORMRepository
from nga.apps.iotron.processing.service import ExternalAgreementService
from nga.apps.references.infra.orm.models import Operator
from nga.apps.references.infra.providers import CountryProvider, OperatorProvider, TrafficSegmentProvider
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum, ServiceTypeEnum, VolumeTypeEnum
from nga.core.types import DatePeriod
from nga.utils.collections import to_id_list
from nga.utils.dt import get_current_datetime_utc
from tests.apps.agreements.fakes import InMemoryBudgetAgreementRepository, InMemoryDiscountRepository
from tests.core.fakes import InMemoryMediator
from tests.factories.agreements import (
    AgreementNegotiatorORMFactory,
    AgreementORMFactory,
    BudgetAgreementFactory,
    BudgetAgreementORMFactory,
)
from tests.factories.agreements.orm import DiscountORMFactory
from tests.factories.budgets import BudgetORMFactory, MasterBudgetORMFactory
from tests.factories.iotron import ExternalAgreementFactory, ExternalAgreementORMFactory
from tests.factories.iotron.dto import DiscountDictFactory, DiscountParameterDictFactory
from tests.factories.references import (
    CountryFactory,
    OperatorFactory,
    OperatorORMFactory,
)


@pytest.mark.django_db
class TestExternalAgreementService:
    def setup_data(
        self,
        do_not_calculate: bool = False,
        terminated_at: Optional[date] = None,
    ) -> tuple[ExternalAgreement, Budget]:
        # Home operator
        home_operator = OperatorORMFactory(pmn_code="DNKTD")

        # Partner Operators
        OperatorORMFactory(pmn_code="THADT")
        OperatorORMFactory(pmn_code="THAWP")

        # Negotiator
        AgreementNegotiatorORMFactory(name="John Botman")

        master_budget = MasterBudgetORMFactory(home_operators=[home_operator])

        discount_import_dict = dict(
            home_operators=["DNKTD"],
            partner_operators=["THADT", "THAWP"],
            direction=DiscountDirectionEnum.BIDIRECTIONAL.name,
            service_types=[ServiceTypeEnum.SMS_MO.name],
            currency_code="EUR",
            settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA.name,
            call_destinations=[],
            called_countries=[],
            traffic_segments=[],
            qualifying_direction=None,
            qualifying_service_types=[],
            qualifying_basis=None,
            qualifying_lower_bound=None,
            qualifying_upper_bound=None,
            imsi_count_type=IMSICountTypeEnum.NO_DATA.name,
        )

        external_agreement = ExternalAgreementORMFactory(
            do_not_calculate=do_not_calculate,
            start_date=date(2021, 1, 1),
            end_date=date(2021, 12, 31),
            home_operators=["DNKTD"],
            partner_operators=["THADT", "THAWP"],
            negotiator="John Botman",
            include_satellite=False,
            include_premium=False,
            include_premium_in_commitment=False,
            is_rolling=False,
            terminated_at=terminated_at,
            discounts=[
                DiscountImportDict(
                    **discount_import_dict,
                    start_date="2021-01-01",
                    end_date="2021-12-31",
                    tax_type=TaxTypeEnum.NET.name,
                    volume_type=VolumeTypeEnum.ACTUAL.name,
                    discount_parameters=[
                        DiscountParameterDictFactory(
                            calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL.name,
                            basis=DiscountBasisEnum.VALUE.name,
                            basis_value="0.0100000000",
                            balancing=None,
                            bound_type=DiscountBoundTypeEnum.VOLUME.name,
                            lower_bound="300000.00",
                        ),
                        DiscountParameterDictFactory(
                            calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED.name,
                            basis=DiscountBasisEnum.VALUE.name,
                            basis_value="0.0300000000",
                            balancing=None,
                            bound_type=DiscountBoundTypeEnum.VOLUME.name,
                            lower_bound="200000.00",
                            upper_bound="300000.00",
                        ),
                        DiscountParameterDictFactory(
                            calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED.name,
                            basis=DiscountBasisEnum.VALUE.name,
                            basis_value="0.0400000000",
                            balancing=None,
                            bound_type=DiscountBoundTypeEnum.VOLUME.name,
                            lower_bound="100000.00",
                            upper_bound="200000.00",
                        ),
                        DiscountParameterDictFactory(
                            calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED.name,
                            basis=DiscountBasisEnum.VALUE.name,
                            basis_value="0.0500000000",
                            balancing=None,
                            bound_type=DiscountBoundTypeEnum.VOLUME.name,
                            lower_bound="0.00",
                            upper_bound="100000.00",
                        ),
                    ],
                ),
                DiscountImportDict(
                    **discount_import_dict,
                    start_date="2021-05-01",
                    end_date="2021-06-30",
                    tax_type=TaxTypeEnum.GROSS.name,
                    volume_type=VolumeTypeEnum.BILLED.name,
                    discount_parameters=[
                        DiscountParameterDictFactory(
                            calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED.name,
                            basis=DiscountBasisEnum.VALUE.name,
                            basis_value="0.**********",
                            balancing=None,
                            bound_type=DiscountBoundTypeEnum.VOLUME.name,
                            lower_bound="100000.00",
                        )
                    ],
                ),
            ],
        )

        return external_agreement, master_budget

    @staticmethod
    def _repository() -> ExternalAgreementDjangoORMRepository:
        return ExternalAgreementDjangoORMRepository(
            operator_provider=OperatorProvider(),
            country_provider=CountryProvider(),
            traffic_segment_provider=TrafficSegmentProvider(),
            agreement_negotiator=AgreementNegotiatorDjangoORMRepository(),
        )

    @staticmethod
    def _service(
        mediator: Mediator = Mediator(),
        budget_agreement_repository: AbstractBudgetAgreementRepository = BudgetAgreementDjangoORMRepository(),
        discount_repository: AbstractDiscountRepository = DiscountDjangoORMRepository(),
    ) -> ExternalAgreementService:
        return ExternalAgreementService(
            mediator=mediator,
            budget_agreement_repository=budget_agreement_repository,
            budget_agreement_renew_service=BudgetAgreementRenewService(
                budget_repository=InMemoryBudgetAgreementRepository(),
                budget_agreement_repository=budget_agreement_repository,
                discount_repository=discount_repository,
            ),
            discount_repository=discount_repository,
        )

    @pytest.mark.parametrize(
        "do_not_calculate, expected_is_active",
        [(True, False), (False, True)],
    )
    def test_without_agreements(
        self,
        do_not_calculate: bool,
        expected_is_active: bool,
        override_deps,
    ):
        external_agreement, master_budget = self.setup_data(do_not_calculate)

        ea_repository = self._repository()
        ea_service = self._service()

        domain_external_agreement = ea_repository.get_by_id(external_agreement.id)

        processed_external_agreement = ea_service.process(domain_external_agreement, budget_id=master_budget.id)

        assert processed_external_agreement.processing_status == ExternalAgreementProcessingStatusEnum.NEW_CREATED

        orm_agreements = models.Agreement.objects
        assert orm_agreements.count() == 1

        orm_agreement = orm_agreements.first()

        assert orm_agreement.name == external_agreement.name
        assert orm_agreement.external_id == external_agreement.external_id
        assert orm_agreement.start_date == external_agreement.start_date
        assert orm_agreement.end_date == external_agreement.end_date
        assert sorted([o.pmn_code for o in orm_agreement.home_operators.all()]) == sorted(
            external_agreement.home_operators
        )
        assert sorted([o.pmn_code for o in orm_agreement.partner_operators.all()]) == sorted(
            external_agreement.partner_operators
        )
        assert orm_agreement.negotiator.name == external_agreement.negotiator

        assert orm_agreement.status == AgreementStatusEnum.LIVE

        budget_agreement = models.BudgetAgreement.objects.get(agreement_id=orm_agreement.id, budget_id=master_budget.id)

        assert budget_agreement.is_active == expected_is_active

        orm_discount = models.Discount.objects.first()

        assert orm_discount.imsi_count_type == IMSICountTypeEnum.NO_DATA

    @patch("nga.apps.iotron.processing.service.DiscountQualifyingUpperBoundFiller")
    @patch("nga.apps.iotron.processing.service.DiscountAboveCommitmentRateFiller")
    @patch("nga.apps.iotron.processing.service.DiscountParentUnifier")
    @patch("nga.apps.iotron.processing.service.DiscountRelationResolver")
    def test_using_discount_updates_during_processing(
        self,
        discount_rel_resolver_mock: Mock,
        discount_parent_unifier_mock: Mock,
        discount_above_commitment_rate_filler_mock: Mock,
        discount_qualifying_upper_bound_filler_mock: Mock,
        override_deps,
    ):
        external_agreement, master_budget = self.setup_data()

        ea_repository = self._repository()
        ea_service = self._service()

        domain_external_agreement = ea_repository.get_by_id(external_agreement.id)

        processed_external_agreement = ea_service.process(domain_external_agreement, budget_id=master_budget.id)

        assert processed_external_agreement.processing_status == ExternalAgreementProcessingStatusEnum.NEW_CREATED

        discount_rel_resolver_mock.assert_called_once()
        discount_rel_resolver_mock.return_value.resolve_sub_discount_rel.assert_called_once()

        discount_parent_unifier_mock.assert_called_once()
        discount_parent_unifier_mock.return_value.unify_parent_discounts.assert_called_once()

        discount_above_commitment_rate_filler_mock.assert_called_once()

        discount_qualifying_upper_bound_filler_mock.assert_called_once()
        discount_qualifying_upper_bound_filler_mock.return_value.fill_qualifying_upper_bound.assert_called_once()

    def test_with_equal_by_external_id_agreement(self):
        external_agreement, master_budget = self.setup_data()

        ea_repository = self._repository()
        ea_service = self._service()

        domain_external_agreement = ea_repository.get_by_id(external_agreement.id)

        agreement = AgreementORMFactory(
            external_id=external_agreement.external_id,
            start_date=date(2010, 1, 1),
            end_date=date(2012, 12, 31),
            home_operators=[OperatorORMFactory(), OperatorORMFactory()],
            partner_operators=[OperatorORMFactory(), OperatorORMFactory()],
            negotiator=AgreementNegotiatorORMFactory(),
            include_satellite=True,
            include_premium=True,
            include_premium_in_commitment=True,
            is_rolling=True,
            created_at=get_current_datetime_utc(),
            updated_at=get_current_datetime_utc(),
        )

        linked_agreement = AgreementORMFactory(
            external_id=None,
            parent_id=agreement.id,
            status=AgreementStatusEnum.AUTO_RENEWED,
        )

        old_discounts = DiscountORMFactory.create_batch(5, agreement=agreement)
        old_discounts_ids = sorted(to_pk_list(old_discounts))

        old_home_operators = sorted([o.pmn_code for o in agreement.home_operators.all()])
        old_partner_operators = sorted([o.pmn_code for o in agreement.partner_operators.all()])
        old_negotiator = agreement.negotiator.name

        BudgetAgreementORMFactory(budget=master_budget, agreement=agreement)
        BudgetAgreementORMFactory(budget=master_budget, agreement=linked_agreement)

        processed_external_agreement = ea_service.process(domain_external_agreement, budget_id=master_budget.id)

        assert processed_external_agreement.processing_status == ExternalAgreementProcessingStatusEnum.UPDATED

        orm_agreements = models.Agreement.objects
        assert orm_agreements.count() == 2

        main_orm_agreement = orm_agreements.get(external_id=external_agreement.external_id)

        assert main_orm_agreement.name != agreement.name
        assert main_orm_agreement.name == external_agreement.name

        assert main_orm_agreement.start_date != agreement.start_date
        assert main_orm_agreement.start_date == external_agreement.start_date

        assert main_orm_agreement.end_date != agreement.end_date
        assert main_orm_agreement.end_date == external_agreement.end_date

        assert sorted([o.pmn_code for o in main_orm_agreement.home_operators.all()]) != old_home_operators
        assert sorted([o.pmn_code for o in main_orm_agreement.home_operators.all()]) == sorted(
            external_agreement.home_operators
        )

        assert sorted([o.pmn_code for o in main_orm_agreement.partner_operators.all()]) != old_partner_operators
        assert sorted([o.pmn_code for o in main_orm_agreement.partner_operators.all()]) == sorted(
            external_agreement.partner_operators
        )

        assert main_orm_agreement.negotiator.name != old_negotiator
        assert main_orm_agreement.negotiator.name == external_agreement.negotiator

        assert main_orm_agreement.include_satellite != agreement.include_satellite
        assert main_orm_agreement.include_satellite == external_agreement.include_satellite

        assert main_orm_agreement.include_premium != agreement.include_premium
        assert main_orm_agreement.include_premium == external_agreement.include_premium

        assert main_orm_agreement.include_premium_in_commitment != agreement.include_premium_in_commitment
        assert main_orm_agreement.include_premium_in_commitment == external_agreement.include_premium_in_commitment

        # assert main_orm_agreement.is_rolling != agreement.is_rolling
        # assert main_orm_agreement.is_rolling == external_agreement.is_rolling
        assert main_orm_agreement.is_rolling is True

        assert main_orm_agreement.discounts.count() == 2
        assert sorted(to_pk_list(main_orm_agreement.discounts.all())) != old_discounts_ids

        assert main_orm_agreement.status == AgreementStatusEnum.LIVE

        assert agreement.created_at == main_orm_agreement.created_at
        assert agreement.updated_at < main_orm_agreement.updated_at

        # validate linked agreement
        linked_orm_agreement = orm_agreements.get(id=linked_agreement.id)

        assert linked_orm_agreement.start_date == date(2022, 1, 1)
        assert linked_orm_agreement.end_date == date(2022, 12, 31)
        assert sorted([o.pmn_code for o in linked_orm_agreement.home_operators.all()]) == sorted(
            external_agreement.home_operators
        )
        assert sorted([o.pmn_code for o in linked_orm_agreement.partner_operators.all()]) == sorted(
            external_agreement.partner_operators
        )
        assert linked_orm_agreement.negotiator.name == external_agreement.negotiator
        assert linked_orm_agreement.status == AgreementStatusEnum.AUTO_RENEWED
        assert linked_orm_agreement.include_satellite == external_agreement.include_satellite
        assert linked_orm_agreement.include_premium == external_agreement.include_premium
        assert linked_orm_agreement.include_premium_in_commitment == external_agreement.include_premium_in_commitment
        # assert linked_orm_agreement.is_rolling == external_agreement.is_rolling

        assert linked_orm_agreement.discounts.count() == main_orm_agreement.discounts.count()

    def test_deactivate_after_modification(self, in_memory_mediator):
        external_agreement, master_budget = self.setup_data(do_not_calculate=False)

        ea_repository = self._repository()
        ea_service = self._service(mediator=in_memory_mediator)

        domain_external_agreement = ea_repository.get_by_id(external_agreement.id)

        agreement = AgreementORMFactory(
            external_id=external_agreement.external_id,
            start_date=date(2010, 1, 1),
            end_date=date(2012, 12, 31),
            home_operators=[OperatorORMFactory(), OperatorORMFactory()],
            partner_operators=[OperatorORMFactory(), OperatorORMFactory()],
            negotiator=AgreementNegotiatorORMFactory(),
            include_satellite=True,
            include_premium=True,
            include_premium_in_commitment=True,
            is_rolling=True,
            created_at=get_current_datetime_utc(),
            updated_at=get_current_datetime_utc(),
        )

        master_ba = BudgetAgreementORMFactory(
            budget=master_budget,
            agreement=agreement,
            calculation_status=AgreementCalculationStatusEnum.APPLIED,
        )

        ba_1 = BudgetAgreementORMFactory(
            agreement=agreement,
            calculation_status=AgreementCalculationStatusEnum.OUTDATED,
        )

        ba_2 = BudgetAgreementORMFactory(
            agreement=agreement,
            calculation_status=AgreementCalculationStatusEnum.NOT_APPLIED,
        )

        ba_3 = BudgetAgreementORMFactory(
            agreement=agreement,
            calculation_status=AgreementCalculationStatusEnum.FAILED,
        )

        processed_external_agreement = ea_service.process(domain_external_agreement, budget_id=master_budget.id)

        assert processed_external_agreement.processing_status == ExternalAgreementProcessingStatusEnum.UPDATED

        orm_agreements = models.Agreement.objects
        assert orm_agreements.count() == 1

        assert len(in_memory_mediator) == 4

        deactivate_ba_list = []
        [deactivate_ba_list.extend(message.budget_agreement_ids) for message in in_memory_mediator.messages]

        assert sorted([master_ba.id, ba_1.id, ba_2.id, ba_3.id]) == sorted(deactivate_ba_list)

    @pytest.mark.parametrize(
        "do_not_calculate, expected_is_active",
        [(True, False), (False, True)],
    )
    @pytest.mark.parametrize(
        "status, start_date, end_date",
        [
            pytest.param(
                AgreementStatusEnum.DRAFT,
                date(2021, 4, 1),
                date(2021, 10, 31),
                id="partial_intersected_with_draft_agreement",
            ),
            pytest.param(
                AgreementStatusEnum.IN_REVIEW,
                date(2021, 4, 1),
                date(2021, 10, 31),
                id="partial_intersected_with_in_review_agreement",
            ),
            pytest.param(
                AgreementStatusEnum.REJECTED,
                date(2021, 4, 1),
                date(2021, 10, 31),
                id="partial_intersected_with_rejected_agreement",
            ),
            pytest.param(
                AgreementStatusEnum.SUBMIT_FAILED,
                date(2021, 4, 1),
                date(2021, 10, 31),
                id="partial_intersected_with_rejected_agreement",
            ),
            pytest.param(
                AgreementStatusEnum.DRAFT,
                date(2021, 1, 1),
                date(2021, 12, 31),
                id="equal_intersected_with_draft_agreement",
            ),
            pytest.param(
                AgreementStatusEnum.IN_REVIEW,
                date(2021, 1, 1),
                date(2021, 12, 31),
                id="equal_intersected_with_in_review_agreement",
            ),
            pytest.param(
                AgreementStatusEnum.REJECTED,
                date(2021, 1, 1),
                date(2021, 12, 31),
                id="equal_intersected_with_rejected_agreement",
            ),
            pytest.param(
                AgreementStatusEnum.SUBMIT_FAILED,
                date(2021, 1, 1),
                date(2021, 12, 31),
                id="equal_intersected_with_rejected_agreement",
            ),
        ],
    )
    def test_with_intersected_or_equal_non_confirmed_agreement(
        self,
        status: AgreementStatusEnum,
        start_date: date,
        end_date: date,
        do_not_calculate: bool,
        expected_is_active: bool,
    ):
        external_agreement, master_budget = self.setup_data(do_not_calculate)

        ea_repository = self._repository()
        ea_service = self._service()

        domain_external_agreement = ea_repository.get_by_id(external_agreement.id)

        agreement = AgreementORMFactory(
            start_date=start_date,
            end_date=end_date,
            home_operators=Operator.objects.filter(pmn_code__in=["DNKTD"]),
            partner_operators=Operator.objects.filter(pmn_code__in=["THADT", "THAWP"]),
            status=status,
        )

        BudgetAgreementORMFactory(budget=master_budget, agreement=agreement)

        processed_external_agreement = ea_service.process(domain_external_agreement, budget_id=master_budget.id)

        assert processed_external_agreement.processing_status == ExternalAgreementProcessingStatusEnum.NEW_CREATED

        orm_agreements = models.Agreement.objects
        assert orm_agreements.count() == 2

        orm_agreement = orm_agreements.filter(status=AgreementStatusEnum.LIVE).first()

        assert orm_agreement.name == external_agreement.name
        assert orm_agreement.external_id == external_agreement.external_id
        assert orm_agreement.include_satellite == external_agreement.include_satellite
        assert orm_agreement.include_premium == external_agreement.include_premium
        assert orm_agreement.include_premium_in_commitment == external_agreement.include_premium_in_commitment
        assert orm_agreement.is_rolling == external_agreement.is_rolling
        assert orm_agreement.start_date == external_agreement.start_date
        assert orm_agreement.end_date == external_agreement.end_date
        assert sorted([o.pmn_code for o in orm_agreement.home_operators.all()]) == sorted(
            external_agreement.home_operators
        )
        assert sorted([o.pmn_code for o in orm_agreement.partner_operators.all()]) == sorted(
            external_agreement.partner_operators
        )
        assert orm_agreement.negotiator.name == external_agreement.negotiator

        budget_agreement = models.BudgetAgreement.objects.get(agreement_id=orm_agreement.id, budget_id=master_budget.id)

        assert budget_agreement.is_active == expected_is_active

    def test_terminate_agreement_instead_of_create(self):
        external_agreement, master_budget = self.setup_data(terminated_at=date(2024, 10, 21))

        ea_repository = self._repository()
        ea_service = self._service()

        domain_external_agreement = ea_repository.get_by_id(external_agreement.id)

        agreement = AgreementORMFactory(
            start_date=external_agreement.start_date,
            end_date=external_agreement.end_date,
            home_operators=Operator.objects.filter(pmn_code__in=["DNKTD"]),
            partner_operators=Operator.objects.filter(pmn_code__in=["THADT", "THAWP"]),
            status=AgreementStatusEnum.IN_REVIEW,
        )

        BudgetAgreementORMFactory(budget=master_budget, agreement=agreement)

        processed_external_agreement = ea_service.process(domain_external_agreement, budget_id=master_budget.id)

        assert processed_external_agreement.processing_status == ExternalAgreementProcessingStatusEnum.TERMINATED

        assert models.Agreement.objects.count() == 1

    def test_terminate_agreement_instead_of_update(self, override_deps):
        external_agreement, master_budget = self.setup_data(terminated_at=date(2024, 10, 21))

        ea_repository = self._repository()
        ea_service = self._service()

        domain_external_agreement = ea_repository.get_by_id(external_agreement.id)

        agreement = AgreementORMFactory(external_id=external_agreement.external_id)

        DiscountORMFactory.create_batch(size=3, agreement=agreement)

        BudgetAgreementORMFactory(budget=master_budget, agreement=agreement)
        BudgetAgreementORMFactory(budget=BudgetORMFactory(), agreement=agreement)

        assert models.Agreement.objects.count() == 1
        assert models.BudgetAgreement.objects.count() == 2
        assert models.Discount.objects.count() == 3

        processed_external_agreement = ea_service.process(domain_external_agreement, budget_id=master_budget.id)

        assert processed_external_agreement.processing_status == ExternalAgreementProcessingStatusEnum.TERMINATED

        assert models.Agreement.objects.count() == 0
        assert models.BudgetAgreement.objects.count() == 0
        assert models.Discount.objects.count() == 0

    def test_with_equal_intersected_agreement_with_submitted_status(
        self,
    ):
        external_agreement, master_budget = self.setup_data()

        ea_repository = self._repository()
        ea_service = self._service()

        domain_external_agreement = ea_repository.get_by_id(external_agreement.id)

        agreement = AgreementORMFactory(
            start_date=external_agreement.start_date,
            end_date=external_agreement.end_date,
            home_operators=Operator.objects.filter(pmn_code__in=["DNKTD"]),
            partner_operators=Operator.objects.filter(pmn_code__in=["THADT", "THAWP"]),
            status=AgreementStatusEnum.SUBMITTED,
        )

        BudgetAgreementORMFactory(budget=master_budget, agreement=agreement)

        processed_external_agreement = ea_service.process(domain_external_agreement, budget_id=master_budget.id)

        assert processed_external_agreement.processing_status == ExternalAgreementProcessingStatusEnum.MOVED_TO_LIVE

        assert models.Agreement.objects.count() == 1

        budget_agreement = models.BudgetAgreement.objects.get(agreement_id=agreement.id, budget_id=master_budget.id)

        assert budget_agreement.is_active is False

    @pytest.mark.parametrize(
        "status, expected_processing_status",
        [
            (AgreementStatusEnum.APPROVED, ExternalAgreementProcessingStatusEnum.SKIPPED),
            (AgreementStatusEnum.LIVE, ExternalAgreementProcessingStatusEnum.SKIPPED),
            (AgreementStatusEnum.CLOSED, ExternalAgreementProcessingStatusEnum.SKIPPED),
            (AgreementStatusEnum.BUDGETING, ExternalAgreementProcessingStatusEnum.SKIPPED),
            (AgreementStatusEnum.AUTO_RENEWED, ExternalAgreementProcessingStatusEnum.SKIPPED),
        ],
    )
    def test_with_equal_intersected_with_confirmed_or_approved_agreement(
        self,
        status: AgreementStatusEnum,
        expected_processing_status,
    ):
        external_agreement, master_budget = self.setup_data()

        ea_repository = self._repository()
        ea_service = self._service()

        domain_external_agreement = ea_repository.get_by_id(external_agreement.id)

        agreement = AgreementORMFactory(
            start_date=external_agreement.start_date,
            end_date=external_agreement.end_date,
            home_operators=Operator.objects.filter(pmn_code__in=["DNKTD"]),
            partner_operators=Operator.objects.filter(pmn_code__in=["THADT", "THAWP"]),
            status=status,
        )

        budget_agreement = BudgetAgreementORMFactory(budget=master_budget, agreement=agreement)

        processed_external_agreement = ea_service.process(domain_external_agreement, budget_id=master_budget.id)

        assert processed_external_agreement.processing_status == expected_processing_status

        assert "equal" in processed_external_agreement.failed_message
        assert str(budget_agreement.id) in processed_external_agreement.failed_message

        assert models.Agreement.objects.count() == 1

        budget_agreement = models.BudgetAgreement.objects.get(agreement_id=agreement.id, budget_id=master_budget.id)

        assert budget_agreement.is_active is False

    @pytest.mark.parametrize(
        "status",
        [
            AgreementStatusEnum.APPROVED,
            AgreementStatusEnum.LIVE,
            AgreementStatusEnum.CLOSED,
            AgreementStatusEnum.BUDGETING,
            AgreementStatusEnum.AUTO_RENEWED,
        ],
    )
    def test_with_partial_intersected_confirmed_or_approved_agreement(self, status: AgreementStatusEnum):
        external_agreement, master_budget = self.setup_data()

        ea_repository = self._repository()
        ea_service = self._service()

        domain_external_agreement = ea_repository.get_by_id(external_agreement.id)

        agreement = AgreementORMFactory(
            start_date=date(2021, 4, 1),
            end_date=date(2021, 10, 31),
            home_operators=Operator.objects.filter(pmn_code__in=["DNKTD"]),
            partner_operators=Operator.objects.filter(pmn_code__in=["THADT", "THAWP"]),
            status=status,
        )

        budget_agreement = BudgetAgreementORMFactory(budget=master_budget, agreement=agreement)

        processed_external_agreement = ea_service.process(domain_external_agreement, budget_id=master_budget.id)

        assert processed_external_agreement.processing_status == ExternalAgreementProcessingStatusEnum.INTERSECTED

        assert "intersected" in processed_external_agreement.failed_message
        assert str(budget_agreement.id) in processed_external_agreement.failed_message

        assert models.Agreement.objects.count() == 1

        budget_agreement = models.BudgetAgreement.objects.get(agreement_id=agreement.id, budget_id=master_budget.id)

        assert budget_agreement.is_active is False

    def test_connect_created_agreement_to_other_budgets(self):
        external_agreement, master_budget = self.setup_data()

        ea_repository = self._repository()
        ea_service = self._service()

        domain_external_agreement = ea_repository.get_by_id(external_agreement.id)

        orm_budgets = BudgetORMFactory.create_batch(
            size=2,
            home_operators=master_budget.home_operators.all(),
            start_date=date(2021, 1, 1),
            end_date=date(2021, 12, 31),
        )

        processed_external_agreement = ea_service.process(domain_external_agreement, budget_id=master_budget.id)

        assert processed_external_agreement.processing_status == ExternalAgreementProcessingStatusEnum.NEW_CREATED

        orm_agreements = models.Agreement.objects
        assert orm_agreements.count() == 1

        orm_agreement = orm_agreements.first()

        budget_agreement = models.BudgetAgreement.objects.filter(agreement_id=orm_agreement.id).exclude(
            budget_id=master_budget.id
        )

        assert len(budget_agreement) == 2

        assert sorted(to_pk_list(orm_budgets)) == sorted([ba.budget_id for ba in budget_agreement])

    def test_discount_model_type_is_evaluated(self):
        external_agreement = ExternalAgreementFactory(
            discounts=[
                DiscountDictFactory(
                    settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA.name,
                    discount_parameters=[
                        DiscountParameterDictFactory(
                            calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE.name,
                            basis=DiscountBasisEnum.VALUE.name,
                            basis_value=Decimal("5"),
                            balancing=DiscountBalancingEnum.NO_BALANCING.name,
                        ),
                    ],
                )
            ]
        )

        budget_agreement = BudgetAgreementFactory()
        budget_agreement_repository = InMemoryBudgetAgreementRepository([budget_agreement])

        mediator = InMemoryMediator(budget_agreement)
        discount_repository = InMemoryDiscountRepository()

        ea_service = self._service(
            mediator=mediator,
            budget_agreement_repository=budget_agreement_repository,
            discount_repository=discount_repository,
        )

        processed_external_agreement = ea_service.process(external_agreement, budget_id=34)

        assert processed_external_agreement.processing_status == ExternalAgreementProcessingStatusEnum.NEW_CREATED

        discounts = discount_repository.get_many(budget_agreement.agreement_id)

        assert len(discounts) == 1

        assert discounts[0].model_type is not None

    def test_discount_model_type_is_evaluated_for_sub_discounts(self):
        sop_discount = DiscountDictFactory(
            settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA.name,
            discount_parameters=[
                DiscountParameterDictFactory(
                    calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL.name,
                    basis=DiscountBasisEnum.VALUE.name,
                    lower_bound=Decimal("5"),
                    balancing=DiscountBalancingEnum.NO_BALANCING.name,
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_COMMITMENT.name,
                ),
            ],
        )

        sre_discount = copy(sop_discount)
        sre_discount["discount_parameters"] = [
            DiscountParameterDictFactory(
                calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE.name,
                basis=DiscountBasisEnum.VALUE.name,
                basis_value=Decimal("5"),
                balancing=DiscountBalancingEnum.NO_BALANCING.name,
            ),
        ]

        external_agreement = ExternalAgreementFactory(discounts=[sop_discount, sre_discount])

        budget_agreement = BudgetAgreementFactory()
        budget_agreement_repository = InMemoryBudgetAgreementRepository([budget_agreement])

        mediator = InMemoryMediator(budget_agreement)
        discount_repository = InMemoryDiscountRepository()

        ea_service = self._service(
            mediator=mediator,
            budget_agreement_repository=budget_agreement_repository,
            discount_repository=discount_repository,
        )

        processed_external_agreement = ea_service.process(external_agreement, budget_id=34)

        assert processed_external_agreement.processing_status == ExternalAgreementProcessingStatusEnum.NEW_CREATED

        discounts = discount_repository.get_many(budget_agreement.agreement_id)
        assert len(discounts) == 1

        parent_discount = discounts[0]

        assert parent_discount.has_sub_discounts is True
        assert parent_discount.sub_discounts[0].model_type is not None

    def test_fill_upper_bound_for_qualifying_rule_in_discounts(self):
        qualifying_fields = dict(
            qualifying_direction=DiscountDirectionEnum.INBOUND.name,
            qualifying_service_types=[ServiceTypeEnum.SMS_MO.name],
            qualifying_basis=DiscountQualifyingBasisEnum.MARKET_SHARE_PERCENTAGE.name,
        )
        discount_parameter = DiscountParameterDictFactory(
            calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE.name,
            basis=DiscountBasisEnum.VALUE.name,
            basis_value=Decimal("5"),
            balancing=DiscountBalancingEnum.NO_BALANCING.name,
        )

        external_agreement = ExternalAgreementFactory(
            discounts=[
                DiscountDictFactory(
                    qualifying_lower_bound="100",
                    discount_parameters=[discount_parameter],
                    **qualifying_fields,
                ),
                DiscountDictFactory(
                    qualifying_lower_bound="0",
                    discount_parameters=[discount_parameter],
                    **qualifying_fields,
                ),
                DiscountDictFactory(
                    qualifying_lower_bound="400",
                    discount_parameters=[discount_parameter],
                    **qualifying_fields,
                ),
                DiscountDictFactory(  # Noize with differ qualifying params
                    qualifying_direction=DiscountDirectionEnum.OUTBOUND.name,
                    qualifying_service_types=[ServiceTypeEnum.SMS_MT.name],
                    qualifying_basis=DiscountQualifyingBasisEnum.MARKET_SHARE_PERCENTAGE.name,
                    qualifying_lower_bound="150",
                    discount_parameters=[discount_parameter],
                ),
                DiscountDictFactory(  # Noize with empty Qualifying Rule
                    qualifying_direction=None,
                    qualifying_service_types=None,
                    qualifying_basis=None,
                    qualifying_lower_bound=None,
                    discount_parameters=[discount_parameter],
                ),
            ]
        )

        budget_agreement = BudgetAgreementFactory()
        budget_agreement_repository = InMemoryBudgetAgreementRepository([budget_agreement])

        mediator = InMemoryMediator(budget_agreement)
        discount_repository = InMemoryDiscountRepository()

        ea_service = self._service(
            mediator=mediator,
            budget_agreement_repository=budget_agreement_repository,
            discount_repository=discount_repository,
        )

        processed_external_agreement = ea_service.process(external_agreement, budget_id=34)

        assert processed_external_agreement.processing_status == ExternalAgreementProcessingStatusEnum.NEW_CREATED

        discounts = discount_repository.get_many(budget_agreement.agreement_id)

        assert len(discounts) == 5

        first_discount = [
            d for d in discounts if d.qualifying_rule is not None and d.qualifying_rule.lower_bound == Decimal("0")
        ][0]
        assert first_discount.qualifying_rule.upper_bound == Decimal("100")

        second_discount = [
            d for d in discounts if d.qualifying_rule is not None and d.qualifying_rule.lower_bound == Decimal("100")
        ][0]
        assert second_discount.qualifying_rule.upper_bound == Decimal("400")

        third_discount = [
            d for d in discounts if d.qualifying_rule is not None and d.qualifying_rule.lower_bound == Decimal("400")
        ][0]
        assert third_discount.qualifying_rule.upper_bound is None

    def test_aggregate_commitment_distribution_parameters(self, override_deps):
        home_operators = [OperatorFactory(pmn_code="DNKTD")]
        partner_operators = [
            OperatorFactory(pmn_code="AIACW"),
            OperatorFactory(pmn_code="ATGCW"),
            OperatorFactory(pmn_code="BHSBH"),
            OperatorFactory(pmn_code="BRBCW"),
            OperatorFactory(pmn_code="CHLVT"),
            OperatorFactory(pmn_code="CYMCW"),
            OperatorFactory(pmn_code="DMACW"),
            OperatorFactory(pmn_code="GRDCW"),
            OperatorFactory(pmn_code="JAMCW"),
            OperatorFactory(pmn_code="KNACW"),
            OperatorFactory(pmn_code="LCACW"),
            OperatorFactory(pmn_code="MSRCW"),
            OperatorFactory(pmn_code="TCACW"),
            OperatorFactory(pmn_code="VCTCW"),
            OperatorFactory(pmn_code="VGBCW"),
        ]
        called_countries = CountryFactory.create_batch(10)

        home_operator_ids = to_id_list(home_operators)
        partner_operator_ids = to_id_list(partner_operators)
        called_country_ids = to_id_list(called_countries)

        discount_factory = partial(
            DiscountDictFactory,
            start_date="2023-01-01",
            end_date="2023-12-31",
            tax_type=TaxTypeEnum.GROSS.name,
            volume_type=VolumeTypeEnum.ACTUAL.name,
            currency_code="USD",
            home_operators=home_operator_ids,
            imsi_count_type=None,
            called_countries=[],
            traffic_segments=[],
            settlement_method=DiscountSettlementMethodEnum.CREDIT_NOTE_EOA.name,
            qualifying_basis=None,
            qualifying_direction=None,
            qualifying_lower_bound=None,
            qualifying_service_types=[],
        )

        external_agreement = ExternalAgreementFactory(
            home_operators=home_operator_ids,
            partner_operators=partner_operator_ids,
            period=DatePeriod(
                start_date=date(2023, 1, 1),
                end_date=date(2023, 12, 31),
            ),
            include_premium=True,
            include_premium_in_commitment=True,
            is_rolling=True,
            discounts=[
                discount_factory(
                    partner_operators=partner_operator_ids,
                    service_types=[ServiceTypeEnum.DATA.name],
                    direction=DiscountDirectionEnum.BIDIRECTIONAL.name,
                    call_destinations=[],
                    discount_parameters=[
                        DiscountParameterDictFactory(
                            basis=DiscountBasisEnum.VALUE.name,
                            basis_value=Decimal("0.0275000000"),
                            calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE.name,
                        ),
                    ],
                ),
                discount_factory(
                    partner_operators=partner_operator_ids,
                    service_types=[ServiceTypeEnum.SMS_MO.name],
                    call_destinations=[c.name for c in CallDestinationEnum],
                    direction=DiscountDirectionEnum.BIDIRECTIONAL.name,
                    discount_parameters=[
                        DiscountParameterDictFactory(
                            basis=DiscountBasisEnum.VALUE.name,
                            basis_value=Decimal("0.0200000000"),
                            calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE.name,
                        ),
                    ],
                ),
                discount_factory(
                    partner_operators=partner_operator_ids,
                    service_types=[ServiceTypeEnum.VOICE_MO.name],
                    call_destinations=[CallDestinationEnum.HOME.name, CallDestinationEnum.LOCAL.name],
                    direction=DiscountDirectionEnum.BIDIRECTIONAL.name,
                    discount_parameters=[
                        DiscountParameterDictFactory(
                            basis=DiscountBasisEnum.VALUE.name,
                            basis_value=Decimal("0.0200000000"),
                            calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE.name,
                        ),
                    ],
                ),
                discount_factory(
                    partner_operators=partner_operator_ids,
                    service_types=[ServiceTypeEnum.VOICE_MO.name],
                    called_countries=called_country_ids[:7],
                    call_destinations=[],
                    direction=DiscountDirectionEnum.BIDIRECTIONAL.name,
                    discount_parameters=[
                        DiscountParameterDictFactory(
                            basis=DiscountBasisEnum.VALUE.name,
                            basis_value=Decimal("0.1000000000"),
                            calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE.name,
                        ),
                    ],
                ),
                discount_factory(
                    partner_operators=partner_operator_ids,
                    service_types=[ServiceTypeEnum.VOICE_MO.name],
                    called_countries=called_country_ids[7:],
                    call_destinations=[],
                    direction=DiscountDirectionEnum.BIDIRECTIONAL.name,
                    discount_parameters=[
                        DiscountParameterDictFactory(
                            basis=DiscountBasisEnum.VALUE.name,
                            basis_value=Decimal("0.1000000000"),
                            calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE.name,
                        ),
                    ],
                ),
                discount_factory(
                    partner_operators=partner_operator_ids,
                    service_types=[ServiceTypeEnum.VOICE_MT.name],
                    called_countries=[],
                    call_destinations=[],
                    direction=DiscountDirectionEnum.BIDIRECTIONAL.name,
                    discount_parameters=[
                        DiscountParameterDictFactory(
                            basis=DiscountBasisEnum.VALUE.name,
                            basis_value=Decimal("0E-10"),
                            calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE.name,
                        ),
                    ],
                ),
                discount_factory(
                    partner_operators=to_id_list(p for p in partner_operators if p.pmn_code != "BHSBH"),
                    service_types=[
                        ServiceTypeEnum.ACCESS_FEE.name,
                        ServiceTypeEnum.DATA.name,
                        ServiceTypeEnum.SMS_MO.name,
                        ServiceTypeEnum.SMS_MT.name,
                        ServiceTypeEnum.VOICE_MO.name,
                        ServiceTypeEnum.VOICE_MT.name,
                        ServiceTypeEnum.VOLTE.name,
                    ],
                    called_countries=[],
                    call_destinations=[],
                    direction=DiscountDirectionEnum.OUTBOUND.name,
                    discount_parameters=[
                        DiscountParameterDictFactory(
                            basis=DiscountBasisEnum.VALUE.name,
                            lower_bound=Decimal("45000.00"),
                            calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL.name,
                        ),
                    ],
                ),
                discount_factory(
                    partner_operators=to_id_list(p for p in partner_operators if p.pmn_code == "BHSBH"),
                    service_types=[
                        ServiceTypeEnum.ACCESS_FEE.name,
                        ServiceTypeEnum.DATA.name,
                        ServiceTypeEnum.SMS_MO.name,
                        ServiceTypeEnum.SMS_MT.name,
                        ServiceTypeEnum.VOICE_MO.name,
                        ServiceTypeEnum.VOICE_MT.name,
                        ServiceTypeEnum.VOLTE.name,
                    ],
                    called_countries=[],
                    call_destinations=[],
                    direction=DiscountDirectionEnum.OUTBOUND.name,
                    discount_parameters=[
                        DiscountParameterDictFactory(
                            basis=DiscountBasisEnum.VALUE.name,
                            lower_bound=Decimal("25000.00"),
                            calculation_type=DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL.name,
                        ),
                    ],
                ),
            ],
        )

        budget_agreement = BudgetAgreementFactory()
        budget_agreement_repository = InMemoryBudgetAgreementRepository([budget_agreement])

        mediator = InMemoryMediator(budget_agreement)
        discount_repository = InMemoryDiscountRepository()

        ea_service = self._service(
            mediator=mediator,
            budget_agreement_repository=budget_agreement_repository,
            discount_repository=discount_repository,
        )

        processed_external_agreement = ea_service.process(external_agreement, budget_id=34)

        assert processed_external_agreement.processing_status == ExternalAgreementProcessingStatusEnum.NEW_CREATED

        discounts = discount_repository.get_many(budget_agreement.agreement_id)

        assert len(discounts) == 7

        sop_financial_discount = [d for d in discounts if d.model_type != DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE][
            0
        ]

        assert len(sop_financial_discount.sub_discounts) == 6
